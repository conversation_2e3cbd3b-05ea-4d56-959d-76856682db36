import axios from 'axios';

// Create axios instance with same configuration as main API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
  withCredentials: false,
});

// Helper function to safely get token
const getAccessToken = () => {
  try {
    return localStorage.getItem('access_token');
  } catch (error) {
    console.error('Error accessing localStorage:', error);
    return null;
  }
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export interface Commission {
  id: string;
  sales_rep: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  client: {
    id: string;
    name: string;
    email: string;
  };
  project: {
    id: string;
    name: string;
    type: string;
  };
  commission_rule?: {
    id: string;
    name: string;
    percentage: number;
  };
  project_amount: number;
  commission_percentage: number;
  commission_amount: number;
  status: 'pending' | 'approved' | 'paid' | 'cancelled';
  status_display: string;
  notes?: string;
  earned_date: string;
  approved_date?: string;
  paid_date?: string;
  is_overdue: boolean;
  created_at: string;
  updated_at: string;
}

export interface CommissionRule {
  id: string;
  name: string;
  rule_type: 'percentage' | 'fixed_amount' | 'tiered';
  percentage: number;
  fixed_amount: number;
  min_project_amount?: number;
  max_project_amount?: number;
  is_active: boolean;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface CommissionPayment {
  id: string;
  commission: string;
  paid_by?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  payment_method: 'bank_transfer' | 'cash' | 'check' | 'digital_wallet';
  payment_status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  payment_amount: number;
  transaction_reference?: string;
  bank_name?: string;
  account_number?: string;
  payment_date: string;
  processed_date?: string;
  notes?: string;
  receipt_url?: string;
  created_at: string;
  updated_at: string;
}

export interface CommissionStats {
  total_commissions: number;
  pending_commissions: number;
  approved_commissions: number;
  paid_commissions: number;
  total_commission_amount: number;
  pending_commission_amount: number;
  paid_commission_amount: number;
  overdue_commissions: number;
  top_sales_reps: Array<{
    sales_rep__id: string;
    sales_rep__first_name: string;
    sales_rep__last_name: string;
    total_commission: number;
    commission_count: number;
  }>;
}

export interface CreateCommissionData {
  sales_rep_id?: string;
  client_id: string;
  project_id: string;
  commission_rule_id?: string;
  project_amount: number;
  commission_percentage?: number;
  notes?: string;
}

export interface CreateCommissionPaymentData {
  commission_id: string;
  payment_method: string;
  payment_amount: number;
  transaction_reference?: string;
  bank_name?: string;
  account_number?: string;
  payment_date: string;
  notes?: string;
}

export interface BulkApprovalData {
  commission_ids: string[];
  notes?: string;
}

export const commissionsAPI = {
  // Commission CRUD operations
  async getCommissions(params?: Record<string, any>) {
    const response = await apiClient.get('/commissions/', { params });
    return response.data;
  },

  async getCommission(id: string) {
    const response = await apiClient.get(`/commissions/${id}/`);
    return response.data;
  },

  async createCommission(data: CreateCommissionData) {
    const response = await apiClient.post('/commissions/', data);
    return response.data;
  },

  async updateCommission(id: string, data: Partial<CreateCommissionData>) {
    const response = await apiClient.patch(`/commissions/${id}/`, data);
    return response.data;
  },

  async deleteCommission(id: string) {
    const response = await apiClient.delete(`/commissions/${id}/`);
    return response.data;
  },

  // Commission statistics
  async getCommissionStats() {
    const response = await apiClient.get('/commissions/stats/');
    return response.data as CommissionStats;
  },

  // Commission approval
  async approveCommission(id: string) {
    const response = await apiClient.post(`/commissions/${id}/approve/`);
    return response.data;
  },

  async bulkApproveCommissions(data: BulkApprovalData) {
    const response = await apiClient.post('/commissions/bulk_approve/', data);
    return response.data;
  },

  async markCommissionAsPaid(id: string) {
    const response = await apiClient.post(`/commissions/${id}/mark_paid/`);
    return response.data;
  },

  // Overdue commissions
  async getOverdueCommissions() {
    const response = await apiClient.get('/commissions/overdue/');
    return response.data;
  },

  // User's commissions
  async getMyCommissions() {
    const response = await apiClient.get('/commissions/my_commissions/');
    return response.data;
  },

  // Commission Rules
  async getCommissionRules(params?: Record<string, any>) {
    const response = await apiClient.get('/commission-rules/', { params });
    return response.data;
  },

  async getCommissionRule(id: string) {
    const response = await apiClient.get(`/commission-rules/${id}/`);
    return response.data;
  },

  async createCommissionRule(data: Partial<CommissionRule>) {
    const response = await apiClient.post('/commission-rules/', data);
    return response.data;
  },

  async updateCommissionRule(id: string, data: Partial<CommissionRule>) {
    const response = await apiClient.patch(`/commission-rules/${id}/`, data);
    return response.data;
  },

  async deleteCommissionRule(id: string) {
    const response = await apiClient.delete(`/commission-rules/${id}/`);
    return response.data;
  },

  // Commission Payments
  async getCommissionPayments(params?: Record<string, any>) {
    const response = await apiClient.get('/commission-payments/', { params });
    return response.data;
  },

  async getCommissionPayment(id: string) {
    const response = await apiClient.get(`/commission-payments/${id}/`);
    return response.data;
  },

  async createCommissionPayment(data: CreateCommissionPaymentData) {
    const response = await apiClient.post('/commission-payments/', data);
    return response.data;
  },

  async updateCommissionPayment(id: string, data: Partial<CreateCommissionPaymentData>) {
    const response = await apiClient.patch(`/commission-payments/${id}/`, data);
    return response.data;
  },

  async deleteCommissionPayment(id: string) {
    const response = await apiClient.delete(`/commission-payments/${id}/`);
    return response.data;
  },
};
