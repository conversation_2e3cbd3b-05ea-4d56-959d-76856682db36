'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Users,
  FolderOpen,
  CheckSquare,
  DollarSign,
  FileText,
  Calendar,
  Settings,
  Zap,
  TrendingUp
} from 'lucide-react';
import { showToast } from '@/lib/toast';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  href: string;
  color: string;
  badge?: string;
  shortcut?: string;
}

const quickActions: QuickAction[] = [
  {
    id: 'add-client',
    title: 'إضافة عميل جديد',
    description: 'إضافة عميل جديد إلى النظام',
    icon: Users,
    href: '/founder-dashboard/clients/new',
    color: 'bg-blue-500 hover:bg-blue-600',
    shortcut: 'Ctrl+N'
  },
  {
    id: 'create-project',
    title: 'إنشاء مشروع',
    description: 'بدء مشروع جديد للعميل',
    icon: FolderOpen,
    href: '/founder-dashboard/projects/new',
    color: 'bg-green-500 hover:bg-green-600',
    shortcut: 'Ctrl+P'
  },
  {
    id: 'assign-task',
    title: 'تعيين مهمة',
    description: 'إنشاء وتعيين مهمة جديدة',
    icon: CheckSquare,
    href: '/founder-dashboard/tasks/new',
    color: 'bg-purple-500 hover:bg-purple-600',
    shortcut: 'Ctrl+T'
  },
  {
    id: 'add-team-member',
    title: 'إضافة عضو فريق',
    description: 'إضافة عضو جديد للفريق',
    icon: Plus,
    href: '/founder-dashboard/team/new',
    color: 'bg-orange-500 hover:bg-orange-600'
  },
  {
    id: 'financial-entry',
    title: 'إدخال مالي',
    description: 'إضافة إيراد أو مصروف',
    icon: DollarSign,
    href: '/founder-dashboard/finance/new',
    color: 'bg-emerald-500 hover:bg-emerald-600'
  },
  {
    id: 'generate-report',
    title: 'إنشاء تقرير',
    description: 'إنشاء تقرير مالي أو إداري',
    icon: FileText,
    href: '/founder-dashboard/reports/new',
    color: 'bg-indigo-500 hover:bg-indigo-600'
  },
  {
    id: 'schedule-meeting',
    title: 'جدولة اجتماع',
    description: 'جدولة اجتماع مع العميل أو الفريق',
    icon: Calendar,
    href: '/founder-dashboard/calendar/new',
    color: 'bg-pink-500 hover:bg-pink-600'
  },
  {
    id: 'system-settings',
    title: 'إعدادات النظام',
    description: 'تكوين إعدادات النظام',
    icon: Settings,
    href: '/founder-dashboard/settings',
    color: 'bg-gray-500 hover:bg-gray-600'
  }
];

interface QuickActionsProps {
  className?: string;
  compact?: boolean;
}

export function QuickActions({ className = '', compact = false }: QuickActionsProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const handleAction = async (action: QuickAction) => {
    try {
      setIsLoading(action.id);
      
      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 200));
      
      router.push(action.href);
      showToast.info(`الانتقال إلى ${action.title}`);
    } catch (error) {
      console.error('Error navigating:', error);
      showToast.error('حدث خطأ في التنقل');
    } finally {
      setIsLoading(null);
    }
  };

  if (compact) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Zap className="h-5 w-5 text-yellow-500" />
            إجراءات سريعة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {quickActions.slice(0, 4).map((action) => (
              <Button
                key={action.id}
                variant="outline"
                size="sm"
                onClick={() => handleAction(action)}
                disabled={isLoading === action.id}
                className="h-auto p-3 flex flex-col items-center gap-2 text-center"
              >
                <action.icon className="h-4 w-4" />
                <span className="text-xs">{action.title}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-yellow-500" />
          الإجراءات السريعة
        </CardTitle>
        <CardDescription>
          إجراءات مهمة يمكن تنفيذها بسرعة
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <Button
              key={action.id}
              variant="outline"
              onClick={() => handleAction(action)}
              disabled={isLoading === action.id}
              className="h-auto p-4 flex flex-col items-center gap-3 text-center hover:shadow-md transition-all duration-200"
            >
              <div className={`p-3 rounded-full text-white ${action.color}`}>
                <action.icon className="h-6 w-6" />
              </div>
              <div className="space-y-1">
                <h4 className="font-medium text-sm">{action.title}</h4>
                <p className="text-xs text-gray-600">{action.description}</p>
                {action.shortcut && (
                  <Badge variant="secondary" className="text-xs">
                    {action.shortcut}
                  </Badge>
                )}
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface QuickStatsProps {
  data: {
    totalRevenue: number;
    activeProjects: number;
    teamMembers: number;
    completedTasks: number;
  };
  className?: string;
}

export function QuickStats({ data, className = '' }: QuickStatsProps) {
  const stats = [
    {
      label: 'إجمالي الإيرادات',
      value: data.totalRevenue.toLocaleString(),
      suffix: 'ج.م',
      icon: DollarSign,
      color: 'text-green-600'
    },
    {
      label: 'المشاريع النشطة',
      value: data.activeProjects.toString(),
      icon: FolderOpen,
      color: 'text-blue-600'
    },
    {
      label: 'أعضاء الفريق',
      value: data.teamMembers.toString(),
      icon: Users,
      color: 'text-purple-600'
    },
    {
      label: 'المهام المكتملة',
      value: data.completedTasks.toString(),
      icon: CheckSquare,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className={`grid grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {stats.map((stat, index) => (
        <Card key={index} className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{stat.label}</p>
              <p className="text-2xl font-bold">
                {stat.value}
                {stat.suffix && <span className="text-sm ml-1">{stat.suffix}</span>}
              </p>
            </div>
            <stat.icon className={`h-8 w-8 ${stat.color}`} />
          </div>
        </Card>
      ))}
    </div>
  );
}

export function RecentActivities({ className = '' }: { className?: string }) {
  const activities = [
    {
      id: 1,
      type: 'project',
      message: 'تم إنشاء مشروع جديد "موقع الشركة التجارية"',
      time: 'منذ ساعتين',
      icon: FolderOpen,
      color: 'text-blue-600'
    },
    {
      id: 2,
      type: 'client',
      message: 'تم إضافة عميل جديد "شركة التقنية المتقدمة"',
      time: 'منذ 4 ساعات',
      icon: Users,
      color: 'text-green-600'
    },
    {
      id: 3,
      type: 'task',
      message: 'تم إكمال مهمة "تصميم الواجهة الرئيسية"',
      time: 'منذ 6 ساعات',
      icon: CheckSquare,
      color: 'text-purple-600'
    },
    {
      id: 4,
      type: 'revenue',
      message: 'تم استلام دفعة مالية بقيمة 15,000 ج.م',
      time: 'منذ يوم واحد',
      icon: TrendingUp,
      color: 'text-emerald-600'
    }
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          النشاط الأخير
        </CardTitle>
        <CardDescription>
          آخر الأنشطة في النظام
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              <div className={`p-2 rounded-full bg-white ${activity.color}`}>
                <activity.icon className="h-4 w-4" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">{activity.message}</p>
                <p className="text-xs text-gray-600">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
