'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import {
  FolderOpen,
  Users,
  Calendar,
  DollarSign,
  Target,
  Globe,
  GitBranch,
  ExternalLink,
  Edit,
  Trash2,
  ArrowRight,
  Clock,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { DEMO_PROJECTS } from '@/lib/demo-data';
import { formatRelativeTime } from '@mtbrmg/shared';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { projectsAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';

export default function ProjectDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [project, setProject] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const loadProject = async () => {
      if (!isAuthenticated || !mounted || !params.id) return;

      setIsLoading(true);
      try {
        // Try to load from API first
        const response = await projectsAPI.getProject(params.id);
        setProject(response);
      } catch (error) {
        console.error('Error loading project:', error);
        // Fallback to demo data
        const demoProject = DEMO_PROJECTS.find(p => p.id === params.id);
        if (demoProject) {
          setProject(demoProject);
        } else {
          showToast.error('المشروع غير موجود');
          router.push('/founder-dashboard/projects');
          return;
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadProject();
  }, [isAuthenticated, mounted, params.id, router]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'planning':
        return <Badge className="bg-blue-100 text-blue-800">التخطيط</Badge>;
      case 'development':
        return <Badge className="bg-yellow-100 text-yellow-800">قيد التطوير</Badge>;
      case 'testing':
        return <Badge className="bg-orange-100 text-orange-800">الاختبار</Badge>;
      case 'deployment':
        return <Badge className="bg-purple-100 text-purple-800">النشر</Badge>;
      case 'maintenance':
        return <Badge className="bg-gray-100 text-gray-800">الصيانة</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">مكتمل</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">ملغي</Badge>;
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>;
      case 'on_hold':
        return <Badge className="bg-yellow-100 text-yellow-800">معلق</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">عالي</Badge>;
      case 'medium':
        return <Badge variant="secondary">متوسط</Badge>;
      case 'low':
        return <Badge variant="outline">منخفض</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'website':
        return <Badge className="bg-blue-100 text-blue-800">موقع إلكتروني</Badge>;
      case 'mobile_app':
        return <Badge className="bg-green-100 text-green-800">تطبيق جوال</Badge>;
      case 'web_app':
        return <Badge className="bg-purple-100 text-purple-800">تطبيق ويب</Badge>;
      case 'ecommerce':
        return <Badge className="bg-orange-100 text-orange-800">متجر إلكتروني</Badge>;
      case 'branding':
        return <Badge className="bg-pink-100 text-pink-800">هوية بصرية</Badge>;
      case 'marketing':
        return <Badge className="bg-yellow-100 text-yellow-800">تسويق رقمي</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 text-purple-600 mx-auto mb-4 animate-spin" />
          <p className="text-gray-600 mt-2">جاري تحميل تفاصيل المشروع...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تفاصيل المشروع</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <Loader2 className="h-12 w-12 text-purple-600 mx-auto mb-4 animate-spin" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">جاري تحميل تفاصيل المشروع...</h3>
              <p className="text-gray-600">يرجى الانتظار</p>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  if (!project) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">المشروع غير موجود</h3>
              <p className="text-gray-600 mb-4">لم يتم العثور على المشروع المطلوب</p>
              <Button onClick={() => router.push('/founder-dashboard/projects')}>
                العودة إلى المشاريع
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/founder-dashboard">لوحة تحكم المؤسس</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/founder-dashboard/projects">إدارة المشاريع</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbPage>{project.name}</BreadcrumbPage>
          </Breadcrumb>
        </div>

        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center">
              <FolderOpen className="h-8 w-8 text-purple-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <p className="text-gray-600 mt-1">{project.description}</p>
              <div className="flex items-center gap-2 mt-3">
                {getTypeBadge(project.type)}
                {getStatusBadge(project.status)}
                {getPriorityBadge(project.priority)}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={() => router.push(`/founder-dashboard/projects/${project.id}/edit`)}>
              <Edit className="h-4 w-4 ml-2" />
              تعديل المشروع
            </Button>
            <Button onClick={() => router.push('/founder-dashboard/projects')}>
              <ArrowRight className="h-4 w-4 ml-2" />
              العودة إلى المشاريع
            </Button>
          </div>
        </div>

        {/* Progress Card */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              تقدم المشروع
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">نسبة الإنجاز</span>
                <span className="text-sm font-bold">{project.progress || 0}%</span>
              </div>
              <Progress value={project.progress || 0} className="h-3" />
              {project.deadline && (
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">الموعد النهائي:</span>
                  <span className="font-medium">{formatDate(project.deadline)}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Project Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Project Information */}
            <Card>
              <CardHeader>
                <CardTitle>معلومات المشروع</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">العميل</label>
                    <p className="text-sm font-semibold">{project.client_name || 'غير محدد'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">نوع المشروع</label>
                    <p className="text-sm font-semibold">{project.type || 'غير محدد'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">تاريخ البداية</label>
                    <p className="text-sm font-semibold">
                      {project.start_date ? formatDate(project.start_date) : 'غير محدد'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">تاريخ النهاية</label>
                    <p className="text-sm font-semibold">
                      {project.end_date ? formatDate(project.end_date) : 'غير محدد'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  المعلومات المالية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">الميزانية</label>
                  <p className="text-lg font-bold text-green-600">
                    {project.budget ? formatCurrency(project.budget) : 'غير محدد'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 ml-2" />
                  إدارة الفريق
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <CheckCircle className="h-4 w-4 ml-2" />
                  إدارة المهام
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Globe className="h-4 w-4 ml-2" />
                  عرض الموقع
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  );
}
