'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Users,
  FolderOpen,
  CheckSquare,
  TrendingUp,
  Calendar,
  AlertTriangle,
  Target,
  Activity,
  Clock,
  Award,
  BarChart3,
  Settings,
  RefreshCw,
  Download,
  Zap,
  Plus
} from 'lucide-react';
import { DEMO_ANALYTICS, DEMO_PROJECTS, DEMO_CLIENTS, DEMO_TASKS, DEMO_TEAM } from '@/lib/demo-data';
import { formatCurrency, formatRelativeTime, getStatusColor } from '@mtbrmg/shared';
import { UnifiedLayout } from '@/components/layout';
import { CurrencyIcon } from '@/components/ui/currency-icon';
import { ConvertedAmountDisplay } from '@/components/ui/currency-conversion-indicator';
import { useCurrency } from '@/lib/stores/currency-store';
import { showToast } from '@/lib/toast';
import { useAuthStore } from '@/lib/stores/auth-store';
import { RoleGuard } from '@/components/auth/role-guard';
import { UserRole } from '@mtbrmg/shared';
import { useDashboardData } from '@/hooks/use-dashboard-data';
import { RecentActivities } from '@/components/dashboard/quick-actions';

export default function FounderDashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { format: formatCurrencyWithStore, refreshRates } = useCurrency();
  const { data: dashboardData, loading: dashboardLoading, error: dashboardError, refreshData } = useDashboardData();
  const [mounted, setMounted] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Currency store now initializes immediately with manual/default rates - no background operations needed
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
      showToast.success('تم تحديث البيانات بنجاح');
    } catch (error) {
      showToast.error('فشل في تحديث البيانات');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle export functionality
  const handleExport = async () => {
    setIsExporting(true);
    try {
      // Simulate export
      await new Promise(resolve => setTimeout(resolve, 2000));
      showToast.success('تم تصدير التقرير بنجاح');
    } catch (error) {
      console.error('Export error:', error);
      showToast.error('فشل في تصدير التقرير');
    } finally {
      setIsExporting(false);
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل لوحة تحكم المؤسس...</p>
        </div>
      </div>
    );
  }

  // Remove the complex loading state for now

  // If not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى لوحة تحكم المؤسس</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Use real data with fallback to demo data
  const { revenue, projects, clients, team } = dashboardData?.metrics || DEMO_ANALYTICS;
  const recentClients = dashboardData?.recentClients || DEMO_CLIENTS;

  // Debug logging
  console.log('FounderDashboard - mounted:', mounted);
  console.log('FounderDashboard - isAuthenticated:', isAuthenticated);
  console.log('FounderDashboard - user:', user);
  console.log('FounderDashboard - DEMO_ANALYTICS:', DEMO_ANALYTICS);

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">

        {/* Remove error banner for now */}

        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              لوحة تحكم المؤسس - MTBRMG ERP
            </h1>
            <p className="text-gray-600 mt-1">
              نظرة شاملة على أداء الوكالة الرقمية
            </p>
            <p className="text-xs text-gray-500 mt-1">
              آخر تحديث: {new Date().toLocaleTimeString('ar-EG')}
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="text-green-600 border-green-600">
              النظام يعمل بكفاءة
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 ml-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'جاري التحديث...' : 'تحديث البيانات'}
            </Button>
            <Button
              variant="outline"
              onClick={handleExport}
              disabled={isExporting}
            >
              <Download className="h-4 w-4 ml-2" />
              {isExporting ? 'جاري التصدير...' : 'تصدير التقرير'}
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/finance/revenue')}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الإيرادات الشهرية</CardTitle>
              <CurrencyIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <ConvertedAmountDisplay
                amount={revenue.current_month}
                className="text-2xl font-bold"
                showOriginal={true}
              />
              <div className="flex items-center text-xs text-green-600">
                <TrendingUp className="h-3 w-3 ml-1" />
                +{revenue.growth}% من الشهر الماضي
              </div>
              <Progress value={(revenue.current_month / revenue.target) * 100} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-1">
                الهدف: {formatCurrency(revenue.target)}
              </p>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/projects')}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المشاريع النشطة</CardTitle>
              <FolderOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{projects.active}</div>
              <p className="text-xs text-muted-foreground">
                {projects.completed_this_month} مكتمل هذا الشهر
              </p>
              {projects.overdue > 0 && (
                <div className="flex items-center text-xs text-red-600 mt-1">
                  <AlertTriangle className="h-3 w-3 ml-1" />
                  {projects.overdue} متأخر عن الموعد
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/clients')}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العملاء</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{clients.total}</div>
              <p className="text-xs text-muted-foreground">
                +{clients.new_this_month} عميل جديد هذا الشهر
              </p>
              <div className="flex gap-2 mt-2">
                <Badge variant="secondary" className="text-xs">
                  😊 {clients.happy}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  😟 {clients.concerned}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/team')}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إنتاجية الفريق</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{team.average_productivity}%</div>
              <p className="text-xs text-muted-foreground">
                {team.completed_tasks_this_week} مهمة مكتملة هذا الأسبوع
              </p>
              <Progress value={team.average_productivity} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-500" />
                الإجراءات السريعة
              </CardTitle>
              <CardDescription>
                إجراءات مهمة يمكن تنفيذها بسرعة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => router.push('/founder-dashboard/clients/new')}
                >
                  <Users className="h-6 w-6 text-blue-600" />
                  <span className="text-sm">إضافة عميل</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => router.push('/founder-dashboard/projects/new')}
                >
                  <FolderOpen className="h-6 w-6 text-green-600" />
                  <span className="text-sm">إنشاء مشروع</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => router.push('/founder-dashboard/tasks/new')}
                >
                  <CheckSquare className="h-6 w-6 text-purple-600" />
                  <span className="text-sm">تعيين مهمة</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => router.push('/founder-dashboard/team/new')}
                >
                  <Users className="h-6 w-6 text-orange-600" />
                  <span className="text-sm">إضافة عضو</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clients Section */}
        <div id="clients" className="mb-8">
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/clients')}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                إدارة العملاء
              </CardTitle>
              <CardDescription>
                نظرة عامة على العملاء وحالتهم
              </CardDescription>
            </CardHeader>
            <CardContent>
              {dashboardLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <div key={i} className="p-4 border rounded-lg animate-pulse">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded mb-1"></div>
                      <div className="h-3 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(recentClients || DEMO_CLIENTS).slice(0, 6).map((client) => (
                  <div
                    key={client.id}
                    className="p-4 border rounded-lg hover:bg-gray-50 hover:shadow-md cursor-pointer transition-all duration-200 group"
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(`/founder-dashboard/clients/${client.id}`);
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium group-hover:text-purple-600 transition-colors">{client.name}</h4>
                      <Badge
                        variant={client.status === 'active' ? 'default' : 'outline'}
                        className={client.status === 'active' ? 'bg-green-100 text-green-800 border-green-200' : ''}
                      >
                        {client.status === 'active' && 'نشط'}
                        {client.status === 'inactive' && 'غير نشط'}
                        {client.status === 'pending' && 'معلق'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-1">{client.email}</p>
                    <p className="text-sm text-gray-600 mb-2">{client.phone}</p>
                    <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-100">
                      <div className="text-xs text-gray-500">
                        {client.projects_count} مشروع
                      </div>
                      <div className="text-xs text-purple-600 opacity-0 group-hover:opacity-100 transition-opacity">
                        عرض التفاصيل ←
                      </div>
                    </div>
                  </div>
                ))}
                </div>
              )}

              {/* Add Client Button */}
              <div className="mt-4 pt-4 border-t">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push('/founder-dashboard/projects/new');
                  }}
                >
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة عميل عبر مشروع جديد
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts and Detailed Views */}
        <div id="projects" className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Recent Projects */}
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/projects')}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                المشاريع الحديثة
              </CardTitle>
              <CardDescription>
                آخر المشاريع وحالة التقدم
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {DEMO_PROJECTS.slice(0, 3).map((project) => (
                  <div key={project.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                       onClick={(e) => {
                         e.stopPropagation();
                         router.push(`/founder-dashboard/projects/${project.id}`);
                       }}>
                    <div className="flex-1">
                      <h4 className="font-medium">{project.name}</h4>
                      <p className="text-sm text-gray-600">{project.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className={getStatusColor(project.status)}>
                          {project.status === 'development' && 'قيد التطوير'}
                          {project.status === 'testing' && 'الاختبار'}
                          {project.status === 'planning' && 'التخطيط'}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {formatCurrency(project.budget || 0)}
                        </span>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="text-2xl font-bold text-purple-600">
                        {project.progress}%
                      </div>
                      <Progress value={project.progress} className="w-16 mt-1" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Team Performance */}
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/team')}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                أداء الفريق
              </CardTitle>
              <CardDescription>
                إحصائيات أعضاء الفريق
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {DEMO_TEAM.slice(0, 4).map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                       onClick={(e) => {
                         e.stopPropagation();
                         const department = member.role === 'sales_manager' ? 'sales' :
                                          member.role === 'developer' ? 'developers' :
                                          member.role === 'designer' ? 'designers' :
                                          member.role === 'media_buyer' ? 'media-buyers' : 'team';
                         router.push(`/founder-dashboard/team/${department}`);
                       }}>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                        {member.name.charAt(0)}
                      </div>
                      <div>
                        <h4 className="font-medium">{member.name}</h4>
                        <p className="text-sm text-gray-600">
                          {member.role === 'sales_manager' && 'مدير المبيعات'}
                          {member.role === 'developer' && 'مطور'}
                          {member.role === 'designer' && 'مصمم'}
                          {member.role === 'media_buyer' && 'مشتري إعلانات'}
                          {member.role === 'wordpress_developer' && 'مطور ووردبريس'}
                        </p>
                      </div>
                    </div>
                    <div className="text-left">
                      <Badge variant="outline">
                        {member.role === 'sales_manager' && `${member.clients_count} عميل`}
                        {member.role === 'developer' && `${member.projects_completed} مشروع`}
                        {member.role === 'designer' && `${member.designs_completed} تصميم`}
                        {member.role === 'media_buyer' && `${member.campaigns_managed} حملة`}
                        {member.role === 'wordpress_developer' && `${member.sites_built} موقع`}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity & Urgent Tasks */}
        <div id="tasks" className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Urgent Tasks */}
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/tasks')}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                المهام العاجلة
              </CardTitle>
              <CardDescription>
                المهام التي تحتاج إلى انتباه فوري
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {DEMO_TASKS.filter(task => task.priority === 'urgent' || task.priority === 'high').map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                       onClick={(e) => {
                         e.stopPropagation();
                         router.push(`/founder-dashboard/tasks/${task.id}`);
                       }}>
                    <div>
                      <h4 className="font-medium">{task.title}</h4>
                      <p className="text-sm text-gray-600">{task.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className={getStatusColor(task.priority)}>
                          {task.priority === 'urgent' && 'عاجل'}
                          {task.priority === 'high' && 'عالي'}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {task.estimated_hours} ساعة
                        </span>
                      </div>
                    </div>
                    <div className="text-left">
                      <Badge variant="outline" className={getStatusColor(task.status)}>
                        {task.status === 'in_progress' && 'قيد التنفيذ'}
                        {task.status === 'review' && 'مراجعة'}
                        {task.status === 'todo' && 'قائمة المهام'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <RecentActivities />
        </div>

        {/* Financial Overview */}
        <div className="mb-8">
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => router.push('/founder-dashboard/finance')}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                النظرة المالية
              </CardTitle>
              <CardDescription>
                ملخص الأداء المالي
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg cursor-pointer hover:bg-green-100 transition-colors"
                     onClick={(e) => {
                       e.stopPropagation();
                       router.push('/founder-dashboard/finance/revenue');
                     }}>
                  <div>
                    <h4 className="font-medium text-green-800">إجمالي الإيرادات</h4>
                    <p className="text-sm text-green-600">هذا الشهر</p>
                  </div>
                  <div className="text-2xl font-bold text-green-800">
                    <ConvertedAmountDisplay
                      amount={revenue.current_month}
                      className="text-2xl font-bold text-green-800"
                    />
                  </div>
                </div>

                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors"
                     onClick={(e) => {
                       e.stopPropagation();
                       router.push('/founder-dashboard/projects');
                     }}>
                  <div>
                    <h4 className="font-medium text-blue-800">المشاريع الجارية</h4>
                    <p className="text-sm text-blue-600">القيمة الإجمالية</p>
                  </div>
                  <div className="text-2xl font-bold text-blue-800">
                    {formatCurrency(DEMO_PROJECTS.reduce((sum, p) => sum + (p.budget || 0), 0))}
                  </div>
                </div>

                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg cursor-pointer hover:bg-purple-100 transition-colors"
                     onClick={(e) => {
                       e.stopPropagation();
                       router.push('/founder-dashboard/analytics');
                     }}>
                  <div>
                    <h4 className="font-medium text-purple-800">متوسط قيمة المشروع</h4>
                    <p className="text-sm text-purple-600">لهذا العام</p>
                  </div>
                  <div className="text-2xl font-bold text-purple-800">
                    {formatCurrency(60000)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settings Section */}
        <div id="settings" className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إعدادات النظام
              </CardTitle>
              <CardDescription>
                إعدادات عامة ومعلومات النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">معلومات المؤسس</h4>
                  <div className="space-y-2">
                    <p><strong>الاسم:</strong> {user?.first_name} {user?.last_name}</p>
                    <p><strong>البريد الإلكتروني:</strong> {user?.email}</p>
                    <p><strong>الهاتف:</strong> {user?.phone}</p>
                    <p><strong>الدور:</strong> مؤسس النظام</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="font-medium">إحصائيات النظام</h4>
                  <div className="space-y-2">
                    <p><strong>إجمالي العملاء:</strong> {clients.total}</p>
                    <p><strong>المشاريع النشطة:</strong> {projects.active}</p>
                    <p><strong>أعضاء الفريق:</strong> {DEMO_TEAM.length}</p>
                    <p><strong>المهام المكتملة:</strong> {team.completed_tasks_this_week}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
}
