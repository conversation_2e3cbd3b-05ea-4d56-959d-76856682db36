'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import {
  Users,
  Save,
  ArrowRight,
  AlertCircle,
  Loader2,
  X
} from 'lucide-react';
import { DEMO_CLIENTS } from '@/lib/demo-data';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { clientsAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';

// Mood options
const MOOD_OPTIONS = [
  { value: 'happy', label: '😊 سعيد', color: 'bg-green-100 text-green-800' },
  { value: 'neutral', label: '😐 محايد', color: 'bg-gray-100 text-gray-800' },
  { value: 'unhappy', label: '😞 غير راضي', color: 'bg-red-100 text-red-800' },
  { value: 'angry', label: '😠 غاضب', color: 'bg-red-200 text-red-900' }
];

// Egyptian governorates
const GOVERNORATES = [
  'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
  'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
  'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
  'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
  'قنا', 'شمال سيناء', 'سوهاج'
];

interface ClientFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  website: string;
  address: string;
  governorate: string;
  mood: string;
  notes: string;
}

export default function EditClientPage() {
  const router = useRouter();
  const params = useParams();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [client, setClient] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<ClientFormData>>({});
  
  const [formData, setFormData] = useState<ClientFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    website: '',
    address: '',
    governorate: '',
    mood: 'neutral',
    notes: ''
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const loadClient = async () => {
      if (!isAuthenticated || !mounted || !params.id) return;

      setIsLoading(true);
      try {
        // Try to load from API first
        const response = await clientsAPI.getClient(params.id);
        setClient(response);
        populateForm(response);
      } catch (error) {
        console.error('Error loading client:', error);
        // Fallback to demo data
        const demoClient = DEMO_CLIENTS.find(c => c.id === params.id);
        if (demoClient) {
          setClient(demoClient);
          populateForm(demoClient);
        } else {
          showToast.error('العميل غير موجود');
          router.push('/founder-dashboard/clients');
          return;
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadClient();
  }, [isAuthenticated, mounted, params.id, router]);

  const populateForm = (clientData: any) => {
    setFormData({
      name: clientData.name || '',
      email: clientData.email || '',
      phone: clientData.phone || '',
      company: clientData.company || '',
      website: clientData.website || '',
      address: clientData.address || '',
      governorate: clientData.governorate || '',
      mood: clientData.mood || 'neutral',
      notes: clientData.notes || ''
    });
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ClientFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم العميل مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Update client via API
      await clientsAPI.updateClient(params.id as string, formData);
      
      showToast.success('تم تحديث بيانات العميل بنجاح! ✅');
      router.push(`/founder-dashboard/clients/${params.id}`);
    } catch (error) {
      console.error('Error updating client:', error);
      showToast.error('حدث خطأ أثناء تحديث العميل. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof ClientFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const selectedMood = MOOD_OPTIONS.find(mood => mood.value === formData.mood);

  if (!mounted) {
    return null;
  }

  if (!isAuthenticated) {
    router.push('/login');
    return null;
  }

  if (isLoading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">جاري تحميل بيانات العميل...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (!client) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">العميل غير موجود</h2>
            <p className="text-gray-600 mb-4">لم يتم العثور على العميل المطلوب</p>
            <Button onClick={() => router.push('/founder-dashboard/clients')}>
              العودة إلى العملاء
            </Button>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbItem>
            <BreadcrumbLink href="/founder-dashboard">لوحة التحكم</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/founder-dashboard/clients">العملاء</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/founder-dashboard/clients/${client.id}`}>
              {client.name}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbPage>تعديل</BreadcrumbPage>
        </Breadcrumb>

        {/* Header */}
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">تعديل العميل</h1>
              <p className="text-gray-600">{client.name}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              onClick={() => router.push(`/founder-dashboard/clients/${client.id}`)}
            >
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
          </div>
        </div>

        {/* Edit Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">المعلومات الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">اسم العميل *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="أدخل اسم العميل"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name}</p>}
                </div>

                <div>
                  <Label htmlFor="email">البريد الإلكتروني *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="أدخل البريد الإلكتروني"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && <p className="text-sm text-red-500 mt-1">{errors.email}</p>}
                </div>

                <div>
                  <Label htmlFor="phone">رقم الهاتف *</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="أدخل رقم الهاتف"
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && <p className="text-sm text-red-500 mt-1">{errors.phone}</p>}
                </div>

                <div>
                  <Label htmlFor="company">اسم الشركة</Label>
                  <Input
                    id="company"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    placeholder="أدخل اسم الشركة (اختياري)"
                  />
                </div>

                <div>
                  <Label htmlFor="website">الموقع الإلكتروني</Label>
                  <Input
                    id="website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="أدخل الموقع الإلكتروني (اختياري)"
                  />
                </div>

                <div>
                  <Label htmlFor="governorate">المحافظة</Label>
                  <Select value={formData.governorate} onValueChange={(value) => handleInputChange('governorate', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر المحافظة" />
                    </SelectTrigger>
                    <SelectContent>
                      {GOVERNORATES.map((gov) => (
                        <SelectItem key={gov} value={gov}>
                          {gov}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="address">العنوان</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="أدخل العنوان التفصيلي"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Client Status & Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">حالة العميل والملاحظات</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="mood">مزاج العميل</Label>
                <Select value={formData.mood} onValueChange={(value) => handleInputChange('mood', value)}>
                  <SelectTrigger>
                    <SelectValue>
                      {selectedMood ? selectedMood.label : 'اختر مزاج العميل'}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {MOOD_OPTIONS.map((mood) => (
                      <SelectItem key={mood.value} value={mood.value}>
                        {mood.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="أدخل أي ملاحظات إضافية عن العميل"
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/founder-dashboard/clients/${client.id}`)}
            >
              <ArrowRight className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ التغييرات
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </UnifiedLayout>
  );
}