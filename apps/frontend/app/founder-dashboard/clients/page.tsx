'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Users,
  Search,
  Plus,
  Mail,
  Phone,
  Building,
  Calendar,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import { DEMO_CLIENTS } from '@/lib/demo-data';
import { formatRelativeTime } from '@mtbrmg/shared';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { AddClientForm, type ClientFormData } from '@/components/forms/add-client-form';
import { EditClientForm } from '@/components/forms/edit-client-form';
import { DeleteClientDialog } from '@/components/dialogs/delete-client-dialog';
import { ClientDetailsDialog } from '@/components/dialogs/client-details-dialog';
import { clientsAPI } from '@/lib/api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function ClientsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isAddClientModalOpen, setIsAddClientModalOpen] = useState(false);
  const [isEditClientModalOpen, setIsEditClientModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [clients, setClients] = useState(DEMO_CLIENTS);

  const handleAddClient = () => {
    setIsAddClientModalOpen(true);
  };

  const handleSubmitClient = async (clientData: ClientFormData) => {
    try {
      console.log('Submitting client data:', clientData);

      // Transform frontend form data to backend API format
      const apiData = {
        name: clientData.name,
        email: clientData.email,
        phone: clientData.phone,
        company: clientData.company || '',
        website: clientData.website || '',
        address: clientData.address || '',
        governorate: clientData.governorate || '',
        mood: clientData.mood,
        sales_rep_id: user?.id ? parseInt(user.id) : null,
        notes: clientData.notes || ''
      };

      console.log('Transformed client API data:', apiData);
      console.log('Current user:', user);
      console.log('Is authenticated:', isAuthenticated);

      // Make API call to create client
      console.log('Making API call to create client...');
      const createdClient = await clientsAPI.createClient(apiData);
      console.log('Client created successfully:', createdClient);

      // Add the new client to local state
      setClients(prev => [createdClient, ...prev]);

      // Close modal
      setIsAddClientModalOpen(false);

      // Show success message
      alert('تم إضافة العميل بنجاح! 🎉');

    } catch (error: any) {
      console.error('Error adding client:', error);

      // Extract error message from API response
      let errorMessage = 'حدث خطأ أثناء إضافة العميل. يرجى المحاولة مرة أخرى.';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else {
          // Handle field-specific errors
          const fieldErrors = Object.entries(error.response.data)
            .map(([field, errors]: [string, any]) => {
              if (Array.isArray(errors)) {
                return `${field}: ${errors.join(', ')}`;
              }
              return `${field}: ${errors}`;
            })
            .join('\n');

          if (fieldErrors) {
            errorMessage = `خطأ في البيانات:\n${fieldErrors}`;
          }
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    }
  };

  const handleEditClient = (client: any) => {
    setSelectedClient(client);
    setIsEditClientModalOpen(true);
  };

  const handleSubmitEditClient = async (clientData: ClientFormData & { id: string }) => {
    try {
      setClients(prev => prev.map(client =>
        client.id === clientData.id
          ? { ...client, ...clientData, updated_at: new Date().toISOString() }
          : client
      ));

      alert('تم تحديث بيانات العميل بنجاح! ✅');

    } catch (error) {
      console.error('Error updating client:', error);
      alert('حدث خطأ أثناء تحديث العميل. يرجى المحاولة مرة أخرى.');
    }
  };

  const handleDeleteClient = (client: any) => {
    setSelectedClient(client);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDeleteClient = async (clientId: string) => {
    try {
      setClients(prev => prev.filter(client => client.id !== clientId));
      alert('تم حذف العميل بنجاح! 🗑️');

    } catch (error) {
      console.error('Error deleting client:', error);
      alert('حدث خطأ أثناء حذف العميل. يرجى المحاولة مرة أخرى.');
    }
  };

  const handleViewClientDetails = (client: any) => {
    router.push(`/founder-dashboard/clients/${client.id}`);
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل إدارة العملاء...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى إدارة العملاء</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Filter clients based on search and status
  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.phone.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || (client as any).status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800">غير نشط</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">معلق</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Users className="h-8 w-8 text-purple-600" />
              إدارة العملاء
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة شاملة لجميع عملاء الوكالة الرقمية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm text-blue-800">
                💡 لإضافة عميل جديد، استخدم نموذج إنشاء المشروع الموحد
              </p>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في العملاء..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={statusFilter === 'all' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('all')}
                >
                  الكل ({DEMO_CLIENTS.length})
                </Button>
                <Button
                  variant={statusFilter === 'active' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('active')}
                >
                  نشط ({DEMO_CLIENTS.filter(c => c.status === 'active').length})
                </Button>
                <Button
                  variant={statusFilter === 'inactive' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('inactive')}
                >
                  غير نشط ({DEMO_CLIENTS.filter(c => c.status === 'inactive').length})
                </Button>
                <Button
                  variant={statusFilter === 'pending' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('pending')}
                >
                  معلق ({DEMO_CLIENTS.filter(c => c.status === 'pending').length})
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Clients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClients.map((client) => (
            <Card
              key={client.id}
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => handleViewClientDetails(client)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{client.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(client.status)}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/founder-dashboard/clients/${client.id}`);
                        }}>
                          <Eye className="h-4 w-4 ml-2" />
                          عرض التفاصيل
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditClient(client)}>
                          <Edit className="h-4 w-4 ml-2" />
                          تعديل
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteClient(client)}
                        >
                          <Trash2 className="h-4 w-4 ml-2" />
                          حذف
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{client.email}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Phone className="h-4 w-4" />
                    <span>{client.phone}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Building className="h-4 w-4" />
                    <span>{client.company || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Calendar className="h-4 w-4" />
                    <span>انضم {formatRelativeTime(client.created_at)}</span>
                  </div>
                  <div className="pt-2 border-t">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">المشاريع:</span>
                      <span className="font-medium">{client.projects_count}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredClients.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عملاء</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'لا توجد عملاء تطابق معايير البحث المحددة'
                  : 'لم يتم إضافة أي عملاء بعد'
                }
              </p>
              {(!searchTerm && statusFilter === 'all') && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                  <p className="text-sm text-blue-800 text-center">
                    💡 لإضافة أول عميل، انتقل إلى صفحة إنشاء المشاريع واختر "إنشاء مشروع مع عميل جديد"
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Add Client Modal */}
        <AddClientForm
          isOpen={isAddClientModalOpen}
          onClose={() => setIsAddClientModalOpen(false)}
          onSubmit={handleSubmitClient}
        />

        {/* Edit Client Modal */}
        <EditClientForm
          isOpen={isEditClientModalOpen}
          onClose={() => setIsEditClientModalOpen(false)}
          onSubmit={handleSubmitEditClient}
          client={selectedClient}
        />

        {/* Delete Client Dialog */}
        <DeleteClientDialog
          isOpen={isDeleteDialogOpen}
          onClose={() => setIsDeleteDialogOpen(false)}
          onConfirm={handleConfirmDeleteClient}
          client={selectedClient}
        />

        {/* Client Details Dialog */}
        <ClientDetailsDialog
          isOpen={isDetailsDialogOpen}
          onClose={() => setIsDetailsDialogOpen(false)}
          onEdit={() => {
            setIsDetailsDialogOpen(false);
            handleEditClient(selectedClient);
          }}
          onDelete={() => {
            setIsDetailsDialogOpen(false);
            handleDeleteClient(selectedClient);
          }}
          client={selectedClient}
        />
      </div>
    </UnifiedLayout>
  );
}
