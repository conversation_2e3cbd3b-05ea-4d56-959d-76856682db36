// Demo data for MTBRMG ERP system
import { User, Client, Project, Task } from '@mtbrmg/shared';

// Demo founder credentials
export const DEMO_FOUNDER_CREDS = {
  username: 'founder',
  email: '<EMAIL>',
  password: 'demo123',
  user: {
    id: '1',
    email: '<EMAIL>',
    first_name: 'محمد',
    last_name: 'يوس<PERSON>',
    username: 'founder',
    role: 'admin' as const,
    status: 'active' as const,
    avatar: '/the_logo.png',
    phone: '+201234567890',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-06-02T00:00:00Z',
    last_login_at: '2024-06-02T00:00:00Z',
  }
};

// Demo clients data
export const DEMO_CLIENTS = [
  {
    id: '1',
    name: 'شركة التقنية المتقدمة',
    email: '<EMAIL>',
    phone: '+201111111111',
    company: 'شركة التقنية المتقدمة',
    website: 'https://techadvanced.com',
    address: 'القاهرة الجديدة، القاهرة',
    governorate: 'cairo',
    mood: 'happy',
    sales_rep_id: '2',
    notes: 'عميل مميز، يطلب دائماً أحدث التقنيات',
    total_projects: 3,
    total_revenue: 150000,
    last_contact_date: '2024-06-01T10:00:00Z',
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-06-01T10:00:00Z',
    status: 'active',
    type: 'company',
    projects_count: 3,
    total_value: 150000,
  },
  {
    id: '2',
    name: 'متجر الأزياء العصرية',
    email: '<EMAIL>',
    phone: '+201222222222',
    company: 'متجر الأزياء العصرية',
    website: 'https://fashionstore.com',
    address: 'الإسكندرية',
    governorate: 'alexandria',
    mood: 'neutral',
    sales_rep_id: '2',
    notes: 'يركز على التجارة الإلكترونية والتسويق الرقمي',
    total_projects: 2,
    total_revenue: 85000,
    last_contact_date: '2024-05-28T14:30:00Z',
    created_at: '2024-02-01T00:00:00Z',
    updated_at: '2024-05-28T14:30:00Z',
    status: 'active',
    type: 'company',
    projects_count: 2,
    total_value: 85000,
  },
  {
    id: '3',
    name: 'مطعم الأصالة',
    email: '<EMAIL>',
    phone: '+201333333333',
    company: 'مطعم الأصالة',
    website: 'https://asala-restaurant.com',
    address: 'الجيزة',
    governorate: 'giza',
    mood: 'concerned',
    sales_rep_id: '3',
    notes: 'يحتاج إلى نظام حجوزات متقدم',
    total_projects: 1,
    total_revenue: 45000,
    last_contact_date: '2024-05-30T16:00:00Z',
    created_at: '2024-03-10T00:00:00Z',
    updated_at: '2024-05-30T16:00:00Z',
    status: 'active',
    type: 'company',
    projects_count: 1,
    total_value: 45000,
  },
];

// Demo projects data
export const DEMO_PROJECTS = [
  {
    id: '1',
    name: 'موقع شركة التقنية المتقدمة',
    description: 'تطوير موقع إلكتروني متقدم مع نظام إدارة المحتوى',
    type: 'website',
    status: 'development',
    priority: 'high',
    client_id: '1',
    client_name: 'شركة التقنية المتقدمة',
    assigned_team: ['4', '5', '6'],
    project_manager_id: '4',
    start_date: '2024-05-01',
    end_date: '2024-07-01',
    deadline: '2024-06-30',
    budget: 75000,
    actual_cost: 45000,
    progress: 65,
    domains: ['techadvanced.com', 'www.techadvanced.com'],
    repository_url: 'https://github.com/mtbrmg/tech-advanced-website',
    staging_url: 'https://staging.techadvanced.com',
    production_url: 'https://techadvanced.com',
    created_at: '2024-05-01T00:00:00Z',
    updated_at: '2024-06-02T00:00:00Z',
  },
  {
    id: '2',
    name: 'متجر الأزياء الإلكتروني',
    description: 'تطوير متجر إلكتروني متكامل مع نظام الدفع',
    type: 'ecommerce',
    status: 'testing',
    priority: 'medium',
    client_id: '2',
    client_name: 'متجر الأزياء العصرية',
    assigned_team: ['4', '7'],
    project_manager_id: '4',
    start_date: '2024-04-15',
    end_date: '2024-06-15',
    deadline: '2024-06-20',
    budget: 60000,
    actual_cost: 52000,
    progress: 85,
    domains: ['fashionstore.com'],
    repository_url: 'https://github.com/mtbrmg/fashion-store',
    staging_url: 'https://staging.fashionstore.com',
    production_url: null,
    created_at: '2024-04-15T00:00:00Z',
    updated_at: '2024-06-02T00:00:00Z',
  },
  {
    id: '3',
    name: 'نظام حجوزات مطعم الأصالة',
    description: 'تطوير نظام حجوزات متقدم مع تطبيق جوال',
    type: 'web_app',
    status: 'planning',
    priority: 'low',
    client_id: '3',
    client_name: 'مطعم الأصالة',
    assigned_team: ['5', '6'],
    project_manager_id: '5',
    start_date: '2024-06-10',
    end_date: '2024-08-10',
    deadline: '2024-08-15',
    budget: 45000,
    actual_cost: 0,
    progress: 15,
    domains: [],
    repository_url: null,
    staging_url: null,
    production_url: null,
    created_at: '2024-06-01T00:00:00Z',
    updated_at: '2024-06-02T00:00:00Z',
  },
];

// Demo tasks data
export const DEMO_TASKS = [
  {
    id: '1',
    title: 'تصميم واجهة المستخدم الرئيسية',
    description: 'تصميم الصفحة الرئيسية وصفحات الخدمات',
    category: 'medium',
    priority: 'high',
    status: 'in_progress',
    project_id: '1',
    assigned_to: ['6'],
    created_by: '4',
    estimated_hours: 16,
    actual_hours: 12,
    due_date: '2024-06-05T00:00:00Z',
    dependencies: [],
    tags: ['تصميم', 'UI/UX'],
    attachments: [],
    created_at: '2024-05-01T00:00:00Z',
    updated_at: '2024-06-02T00:00:00Z',
    completed_at: null,
  },
  {
    id: '2',
    title: 'تطوير نظام إدارة المحتوى',
    description: 'برمجة لوحة تحكم إدارة المحتوى',
    category: 'extreme',
    priority: 'high',
    status: 'in_progress',
    project_id: '1',
    assigned_to: ['4', '5'],
    created_by: '4',
    estimated_hours: 24,
    actual_hours: 18,
    due_date: '2024-06-10T00:00:00Z',
    dependencies: ['1'],
    tags: ['برمجة', 'CMS'],
    attachments: [],
    created_at: '2024-05-05T00:00:00Z',
    updated_at: '2024-06-02T00:00:00Z',
    completed_at: null,
  },
  {
    id: '3',
    title: 'اختبار نظام الدفع',
    description: 'اختبار تكامل بوابات الدفع المختلفة',
    category: 'light',
    priority: 'urgent',
    status: 'review',
    project_id: '2',
    assigned_to: ['7'],
    created_by: '4',
    estimated_hours: 4,
    actual_hours: 3,
    due_date: '2024-06-03T00:00:00Z',
    dependencies: [],
    tags: ['اختبار', 'دفع'],
    attachments: [],
    created_at: '2024-05-25T00:00:00Z',
    updated_at: '2024-06-01T00:00:00Z',
    completed_at: null,
  },
];

// Demo team members
export const DEMO_TEAM = [
  {
    id: '2',
    name: 'أحمد محمد',
    role: 'sales_manager',
    email: '<EMAIL>',
    avatar: null,
    clients_count: 15,
    revenue_generated: 280000,
  },
  {
    id: '3',
    name: 'فاطمة علي',
    role: 'media_buyer',
    email: '<EMAIL>',
    avatar: null,
    campaigns_managed: 25,
    ad_spend: 150000,
  },
  {
    id: '4',
    name: 'محمود حسن',
    role: 'developer',
    email: '<EMAIL>',
    avatar: null,
    projects_completed: 12,
    technologies: ['React', 'Node.js', 'Python'],
  },
  {
    id: '5',
    name: 'سارة أحمد',
    role: 'developer',
    email: '<EMAIL>',
    avatar: null,
    projects_completed: 8,
    technologies: ['Vue.js', 'Laravel', 'MySQL'],
  },
  {
    id: '6',
    name: 'عمر خالد',
    role: 'designer',
    email: '<EMAIL>',
    avatar: null,
    designs_completed: 45,
    specialties: ['UI/UX', 'Branding', 'Motion Graphics'],
  },
  {
    id: '7',
    name: 'نور الدين',
    role: 'wordpress_developer',
    email: '<EMAIL>',
    avatar: null,
    sites_built: 20,
    plugins_developed: 5,
  },
];

// Demo analytics data
export const DEMO_ANALYTICS = {
  revenue: {
    current_month: 85000,
    last_month: 72000,
    growth: 18.1,
    target: 100000,
  },
  projects: {
    active: 8,
    completed_this_month: 3,
    overdue: 1,
    total: 24,
  },
  clients: {
    total: 18,
    new_this_month: 2,
    happy: 12,
    concerned: 3,
    angry: 1,
  },
  team: {
    total_members: 6,
    active_tasks: 15,
    completed_tasks_this_week: 8,
    average_productivity: 87,
  },
};
