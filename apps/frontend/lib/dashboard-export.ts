'use client';

import { DashboardData } from '@/hooks/use-dashboard-data';
import { formatCurrency } from '@mtbrmg/shared';

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  sections: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  includeCharts?: boolean;
  language?: 'ar' | 'en';
}

export class DashboardExporter {
  private data: DashboardData;

  constructor(data: DashboardData) {
    this.data = data;
  }

  async exportToPDF(options: Partial<ExportOptions> = {}): Promise<Blob> {
    // Simulate PDF generation
    const reportContent = this.generateReportContent(options);
    
    // In a real implementation, you would use a library like jsPDF or Puppeteer
    const pdfContent = `
      MTBRMG ERP - تقرير لوحة التحكم
      =====================================
      
      ${reportContent}
    `;
    
    return new Blob([pdfContent], { type: 'application/pdf' });
  }

  async exportToExcel(options: Partial<ExportOptions> = {}): Promise<Blob> {
    // Simulate Excel generation
    const csvContent = this.generateCSVContent(options);
    
    // In a real implementation, you would use a library like SheetJS
    return new Blob([csvContent], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }

  async exportToCSV(options: Partial<ExportOptions> = {}): Promise<Blob> {
    const csvContent = this.generateCSVContent(options);
    return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  }

  private generateReportContent(options: Partial<ExportOptions>): string {
    const { metrics } = this.data;
    const timestamp = new Date().toLocaleString('ar-EG');
    
    let content = `تاريخ التقرير: ${timestamp}\n\n`;
    
    // Executive Summary
    content += `الملخص التنفيذي:\n`;
    content += `================\n`;
    content += `الإيرادات الشهرية: ${formatCurrency(metrics.revenue.current_month)}\n`;
    content += `نمو الإيرادات: ${metrics.revenue.growth}%\n`;
    content += `المشاريع النشطة: ${metrics.projects.active}\n`;
    content += `إجمالي العملاء: ${metrics.clients.total}\n`;
    content += `أعضاء الفريق: ${metrics.team.total_members}\n`;
    content += `إنتاجية الفريق: ${metrics.team.average_productivity}%\n\n`;
    
    // Financial Metrics
    content += `المؤشرات المالية:\n`;
    content += `==================\n`;
    content += `الإيرادات الحالية: ${formatCurrency(metrics.revenue.current_month)}\n`;
    content += `الإيرادات السابقة: ${formatCurrency(metrics.revenue.last_month)}\n`;
    content += `الهدف الشهري: ${formatCurrency(metrics.revenue.target)}\n`;
    content += `إجمالي قيمة المشاريع: ${formatCurrency(metrics.projects.total_value)}\n`;
    content += `متوسط قيمة المشروع: ${formatCurrency(metrics.projects.average_value)}\n\n`;
    
    // Project Metrics
    content += `مؤشرات المشاريع:\n`;
    content += `==================\n`;
    content += `المشاريع النشطة: ${metrics.projects.active}\n`;
    content += `المشاريع المكتملة هذا الشهر: ${metrics.projects.completed_this_month}\n`;
    content += `المشاريع المتأخرة: ${metrics.projects.overdue}\n`;
    content += `إجمالي المشاريع: ${metrics.projects.total}\n\n`;
    
    // Client Metrics
    content += `مؤشرات العملاء:\n`;
    content += `================\n`;
    content += `إجمالي العملاء: ${metrics.clients.total}\n`;
    content += `العملاء الجدد هذا الشهر: ${metrics.clients.new_this_month}\n`;
    content += `العملاء الراضون: ${metrics.clients.happy}\n`;
    content += `العملاء المهتمون: ${metrics.clients.concerned}\n\n`;
    
    // Team Metrics
    content += `مؤشرات الفريق:\n`;
    content += `===============\n`;
    content += `إجمالي أعضاء الفريق: ${metrics.team.total_members}\n`;
    content += `المهام النشطة: ${metrics.team.active_tasks}\n`;
    content += `المهام المكتملة هذا الأسبوع: ${metrics.team.completed_tasks_this_week}\n`;
    content += `متوسط الإنتاجية: ${metrics.team.average_productivity}%\n\n`;
    
    // Recent Projects
    if (this.data.recentProjects.length > 0) {
      content += `المشاريع الحديثة:\n`;
      content += `=================\n`;
      this.data.recentProjects.forEach((project, index) => {
        content += `${index + 1}. ${project.name}\n`;
        content += `   الوصف: ${project.description}\n`;
        content += `   الحالة: ${project.status}\n`;
        content += `   التقدم: ${project.progress}%\n`;
        if (project.budget) {
          content += `   الميزانية: ${formatCurrency(project.budget)}\n`;
        }
        content += `\n`;
      });
    }
    
    // Urgent Tasks
    if (this.data.urgentTasks.length > 0) {
      content += `المهام العاجلة:\n`;
      content += `===============\n`;
      this.data.urgentTasks.forEach((task, index) => {
        content += `${index + 1}. ${task.title}\n`;
        content += `   الوصف: ${task.description}\n`;
        content += `   الأولوية: ${task.priority}\n`;
        content += `   الحالة: ${task.status}\n`;
        content += `\n`;
      });
    }
    
    return content;
  }

  private generateCSVContent(options: Partial<ExportOptions>): string {
    const { metrics } = this.data;
    
    let csv = 'المؤشر,القيمة,الوحدة\n';
    
    // Financial metrics
    csv += `الإيرادات الشهرية,${metrics.revenue.current_month},ج.م\n`;
    csv += `نمو الإيرادات,${metrics.revenue.growth},%\n`;
    csv += `الهدف الشهري,${metrics.revenue.target},ج.م\n`;
    
    // Project metrics
    csv += `المشاريع النشطة,${metrics.projects.active},عدد\n`;
    csv += `المشاريع المكتملة,${metrics.projects.completed_this_month},عدد\n`;
    csv += `المشاريع المتأخرة,${metrics.projects.overdue},عدد\n`;
    csv += `إجمالي قيمة المشاريع,${metrics.projects.total_value},ج.م\n`;
    
    // Client metrics
    csv += `إجمالي العملاء,${metrics.clients.total},عدد\n`;
    csv += `العملاء الجدد,${metrics.clients.new_this_month},عدد\n`;
    csv += `العملاء الراضون,${metrics.clients.happy},عدد\n`;
    
    // Team metrics
    csv += `أعضاء الفريق,${metrics.team.total_members},عدد\n`;
    csv += `المهام النشطة,${metrics.team.active_tasks},عدد\n`;
    csv += `المهام المكتملة,${metrics.team.completed_tasks_this_week},عدد\n`;
    csv += `متوسط الإنتاجية,${metrics.team.average_productivity},%\n`;
    
    return csv;
  }

  static async downloadFile(blob: Blob, filename: string): Promise<void> {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  static generateFilename(format: string, prefix: string = 'dashboard-report'): string {
    const timestamp = new Date().toISOString().split('T')[0];
    return `${prefix}-${timestamp}.${format}`;
  }
}

export async function exportDashboard(
  data: DashboardData, 
  format: 'pdf' | 'excel' | 'csv' = 'pdf',
  options: Partial<ExportOptions> = {}
): Promise<void> {
  const exporter = new DashboardExporter(data);
  
  let blob: Blob;
  let extension: string;
  
  switch (format) {
    case 'pdf':
      blob = await exporter.exportToPDF(options);
      extension = 'pdf';
      break;
    case 'excel':
      blob = await exporter.exportToExcel(options);
      extension = 'xlsx';
      break;
    case 'csv':
      blob = await exporter.exportToCSV(options);
      extension = 'csv';
      break;
    default:
      throw new Error(`Unsupported format: ${format}`);
  }
  
  const filename = DashboardExporter.generateFilename(extension, 'mtbrmg-dashboard');
  await DashboardExporter.downloadFile(blob, filename);
}

export function getAvailableExportFormats(): Array<{value: string, label: string}> {
  return [
    { value: 'pdf', label: 'PDF' },
    { value: 'excel', label: 'Excel (XLSX)' },
    { value: 'csv', label: 'CSV' }
  ];
}

export function getAvailableExportSections(): Array<{value: string, label: string}> {
  return [
    { value: 'summary', label: 'الملخص التنفيذي' },
    { value: 'financial', label: 'المؤشرات المالية' },
    { value: 'projects', label: 'مؤشرات المشاريع' },
    { value: 'clients', label: 'مؤشرات العملاء' },
    { value: 'team', label: 'مؤشرات الفريق' },
    { value: 'recent_projects', label: 'المشاريع الحديثة' },
    { value: 'urgent_tasks', label: 'المهام العاجلة' }
  ];
}
