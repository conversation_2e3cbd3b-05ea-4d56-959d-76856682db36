/**
 * Currency Conversion Service for MTBRMG ERP
 * Handles real-time currency conversion with caching and fallback strategies
 */

export interface ExchangeRates {
  [currency: string]: number;
}

export interface CurrencyData {
  rates: ExchangeRates;
  lastUpdate: string;
  nextUpdate: string;
  source: string;
  baseCurrency: string;
}

export interface ConversionResult {
  success: boolean;
  convertedAmount: number;
  rate: number;
  fromCurrency: string;
  toCurrency: string;
  timestamp: string;
  source: 'api' | 'cache' | 'fallback';
}

class CurrencyConversionService {
  private readonly BASE_CURRENCY = 'EGP';
  private readonly CACHE_KEY = 'mtbrmg-exchange-rates';
  private readonly MANUAL_RATES_KEY = 'mtbrmg-manual-exchange-rates';
  private readonly CACHE_DURATION = 6 * 60 * 60 * 1000; // 6 hours in milliseconds

  // CurrencyFreaks API configuration (DISABLED)
  private readonly API_KEY = '916fe0d7be8948f59de8edb206756d05';
  private readonly PRIMARY_API = 'https://billing.currencyfreaks.com/v2.0/rates/latest';

  // Default fallback rates (used when no manual rates are set)
  private readonly DEFAULT_RATES: ExchangeRates = {
    USD: 0.032,     // 1 EGP ≈ 0.032 USD (approximately 31 EGP = 1 USD)
    SAR: 0.12,      // 1 EGP ≈ 0.12 SAR (approximately 8.3 EGP = 1 SAR)
    AED: 0.118,     // 1 EGP ≈ 0.118 AED (approximately 8.5 EGP = 1 AED)
    EGP: 1.0        // Base currency
  };

  /**
   * Get current exchange rates with caching and immediate fallback
   */
  async getExchangeRates(forceRefresh = false): Promise<CurrencyData> {
    try {
      console.log('CurrencyService: Getting exchange rates', { forceRefresh });

      // Check cache first unless force refresh
      if (!forceRefresh) {
        const cachedData = this.getCachedRates();
        if (cachedData && this.isCacheValid(cachedData)) {
          console.log('CurrencyService: Using valid cached rates');
          return cachedData;
        }
      }

      // Try to fetch fresh rates from API with timeout
      try {
        const freshData = await this.fetchRatesFromAPI();
        if (freshData) {
          console.log('CurrencyService: Got fresh rates from API');
          this.cacheRates(freshData);
          return freshData;
        }
      } catch (apiError) {
        console.warn('CurrencyService: API fetch failed, falling back to cache/fallback', apiError);
      }

      // Fallback to cached data even if expired
      const cachedData = this.getCachedRates();
      if (cachedData) {
        console.log('CurrencyService: Using expired cached rates');
        return { ...cachedData, source: 'cache' };
      }

      // Last resort: use fallback rates
      console.log('CurrencyService: Using fallback rates');
      return this.getFallbackRates();

    } catch (error) {
      console.error('CurrencyService: Error getting exchange rates:', error);

      // Always return fallback rates to ensure app doesn't break
      return this.getFallbackRates();
    }
  }

  /**
   * Get exchange rates immediately with manual/fallback rates (non-blocking)
   */
  getExchangeRatesImmediate(): CurrencyData {
    console.log('CurrencyService: Getting immediate exchange rates');

    // Try manual rates first
    const manualRates = this.getManualRates();
    if (manualRates) {
      console.log('CurrencyService: Using manual rates immediately');
      return manualRates;
    }

    // Try cache second
    const cachedData = this.getCachedRates();
    if (cachedData) {
      console.log('CurrencyService: Using cached rates immediately');
      return cachedData;
    }

    // Return default rates immediately
    console.log('CurrencyService: Using default rates immediately');
    return this.getDefaultRates();
  }

  /**
   * Get manually set exchange rates from localStorage
   */
  getManualRates(): CurrencyData | null {
    try {
      if (typeof window === 'undefined') return null;

      const stored = localStorage.getItem(this.MANUAL_RATES_KEY);
      if (!stored) return null;

      const data = JSON.parse(stored);
      console.log('CurrencyService: Found manual rates', data);
      return data;
    } catch (error) {
      console.error('CurrencyService: Error getting manual rates:', error);
      return null;
    }
  }

  /**
   * Set manual exchange rates
   * @param rates - Exchange rates object (e.g., { USD: 0.02, SAR: 0.075 })
   */
  setManualRates(rates: Partial<ExchangeRates>): void {
    try {
      if (typeof window === 'undefined') return;

      // Merge with existing rates, ensuring EGP is always 1.0
      const currentRates = this.getManualRates()?.rates || this.DEFAULT_RATES;
      const updatedRates = { ...currentRates, ...rates, EGP: 1.0 };

      const manualData: CurrencyData = {
        rates: updatedRates,
        lastUpdate: new Date().toISOString(),
        nextUpdate: new Date(Date.now() + this.CACHE_DURATION).toISOString(),
        source: 'manual',
        baseCurrency: this.BASE_CURRENCY
      };

      localStorage.setItem(this.MANUAL_RATES_KEY, JSON.stringify(manualData));
      console.log('CurrencyService: Manual rates saved', updatedRates);
    } catch (error) {
      console.error('CurrencyService: Error setting manual rates:', error);
    }
  }

  /**
   * Set exchange rate for a specific currency using the format "1 USD = X EGP"
   * @param currency - Target currency (USD, SAR, AED)
   * @param egpValue - How many EGP equals 1 unit of the currency (e.g., 50 for "1 USD = 50 EGP")
   */
  setExchangeRateFromEGP(currency: string, egpValue: number): void {
    if (egpValue <= 0) {
      console.error('CurrencyService: Invalid EGP value, must be positive');
      return;
    }

    // Convert "1 USD = 50 EGP" to rate format (1 EGP = 0.02 USD)
    const rate = 1 / egpValue;

    console.log(`CurrencyService: Setting rate - 1 ${currency} = ${egpValue} EGP (rate: ${rate})`);
    this.setManualRates({ [currency]: rate });
  }

  /**
   * Get current exchange rate in "1 Currency = X EGP" format
   * @param currency - Target currency
   * @returns EGP value for 1 unit of the currency
   */
  getEGPValueForCurrency(currency: string): number {
    const rates = this.getManualRates()?.rates || this.DEFAULT_RATES;
    const rate = rates[currency];

    if (!rate || rate === 0) return 0;

    // Convert rate to "1 Currency = X EGP" format
    return 1 / rate;
  }

  /**
   * Reset to default rates
   */
  resetToDefaultRates(): void {
    try {
      if (typeof window === 'undefined') return;

      localStorage.removeItem(this.MANUAL_RATES_KEY);
      console.log('CurrencyService: Reset to default rates');
    } catch (error) {
      console.error('CurrencyService: Error resetting rates:', error);
    }
  }

  /**
   * Convert amount from one currency to another
   */
  async convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<ConversionResult> {
    try {
      if (fromCurrency === toCurrency) {
        return {
          success: true,
          convertedAmount: amount,
          rate: 1,
          fromCurrency,
          toCurrency,
          timestamp: new Date().toISOString(),
          source: 'api'
        };
      }

      const currencyData = await this.getExchangeRates();
      
      // Convert to base currency (EGP) first if needed
      let amountInBase = amount;
      if (fromCurrency !== this.BASE_CURRENCY) {
        const fromRate = currencyData.rates[fromCurrency];
        if (!fromRate) {
          throw new Error(`Exchange rate not found for ${fromCurrency}`);
        }
        amountInBase = amount / fromRate;
      }

      // Convert from base currency to target currency
      let convertedAmount = amountInBase;
      let rate = 1;
      
      if (toCurrency !== this.BASE_CURRENCY) {
        rate = currencyData.rates[toCurrency];
        if (!rate) {
          throw new Error(`Exchange rate not found for ${toCurrency}`);
        }
        convertedAmount = amountInBase * rate;
      }

      return {
        success: true,
        convertedAmount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
        rate: fromCurrency === this.BASE_CURRENCY ? rate : (rate / currencyData.rates[fromCurrency]),
        fromCurrency,
        toCurrency,
        timestamp: currencyData.lastUpdate,
        source: currencyData.source as 'api' | 'cache' | 'fallback'
      };

    } catch (error) {
      console.error('Currency conversion error:', error);
      return {
        success: false,
        convertedAmount: amount,
        rate: 1,
        fromCurrency,
        toCurrency,
        timestamp: new Date().toISOString(),
        source: 'fallback'
      };
    }
  }

  /**
   * Fetch rates from API - DISABLED for manual rate management
   * Always returns null to force use of static/manual rates
   */
  private async fetchRatesFromAPI(): Promise<CurrencyData | null> {
    // API calls completely disabled for manual rate management
    console.log('CurrencyService: API calls disabled - using manual/static rates only');
    return null;

    /* DISABLED - All network operations commented out for manual rate management
    try {
      console.log('CurrencyService: Fetching rates from CurrencyFreaks API');

      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 seconds timeout

      const response = await fetch(`${this.PRIMARY_API}?apikey=${this.API_KEY}&base=${this.BASE_CURRENCY}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`CurrencyFreaks API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('CurrencyService: API response received', { success: !!data.rates });

      if (!data.rates) {
        throw new Error('Invalid API response: missing rates data');
      }

      return {
        rates: data.rates,
        lastUpdate: data.date || new Date().toISOString(),
        nextUpdate: new Date(Date.now() + this.CACHE_DURATION).toISOString(),
        source: 'api',
        baseCurrency: this.BASE_CURRENCY
      };

    } catch (error) {
      if (error.name === 'AbortError') {
        console.warn('CurrencyService: API request timed out after 3 seconds');
      } else {
        console.error('CurrencyService: API fetch error:', error);
      }
      return null;
    }
    */
  }

  /**
   * Get cached exchange rates
   */
  private getCachedRates(): CurrencyData | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (!cached) return null;
      
      return JSON.parse(cached);
    } catch (error) {
      console.error('Cache read error:', error);
      return null;
    }
  }

  /**
   * Cache exchange rates
   */
  private cacheRates(data: CurrencyData): void {
    try {
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Cache write error:', error);
    }
  }

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(data: CurrencyData): boolean {
    try {
      const lastUpdate = new Date(data.lastUpdate);
      const now = new Date();
      const timeDiff = now.getTime() - lastUpdate.getTime();
      
      return timeDiff < this.CACHE_DURATION;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get default rates when no manual rates are set
   */
  private getDefaultRates(): CurrencyData {
    return {
      rates: this.DEFAULT_RATES,
      lastUpdate: new Date().toISOString(),
      nextUpdate: new Date(Date.now() + this.CACHE_DURATION).toISOString(),
      source: 'default',
      baseCurrency: this.BASE_CURRENCY
    };
  }

  /**
   * Get fallback rates (alias for getDefaultRates for backward compatibility)
   */
  private getFallbackRates(): CurrencyData {
    return this.getDefaultRates();
  }

  /**
   * Clear cached rates (useful for testing or manual refresh)
   */
  clearCache(): void {
    try {
      localStorage.removeItem(this.CACHE_KEY);
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  /**
   * Get cache status for UI display
   */
  getCacheStatus(): { hasCache: boolean; lastUpdate?: string; isValid?: boolean } {
    const cached = this.getCachedRates();
    if (!cached) {
      return { hasCache: false };
    }

    return {
      hasCache: true,
      lastUpdate: cached.lastUpdate,
      isValid: this.isCacheValid(cached)
    };
  }
}

// Export singleton instance
export const currencyConversionService = new CurrencyConversionService();
