import axios from 'axios';
import { LoginData, RegisterData, TokenResponse, User } from '@mtbrmg/shared';

// Export commissions API
export { commissionsAPI } from './api/commissions';

// Professional API configuration with fallback
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
const API_FALLBACK_URL = process.env.NEXT_PUBLIC_API_URL_FALLBACK || 'http://localhost:8000/api';

// Test API connectivity and use fallback if needed
let currentApiUrl = API_BASE_URL;

// Create axios instance with enhanced configuration
const api = axios.create({
  baseURL: currentApiUrl,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
  withCredentials: false, // Disable credentials for CORS
});

// Helper function to safely get token
const getAccessToken = () => {
  try {
    return localStorage.getItem('access_token');
  } catch (error) {
    console.error('Error accessing localStorage:', error);
    return null;
  }
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh and API fallback
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle network errors with fallback
    if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNREFUSED' || !error.response) {
      console.warn('Network error detected, trying fallback API...', error.message);

      // Try fallback URL if not already using it
      if (currentApiUrl !== API_FALLBACK_URL && !originalRequest._fallbackRetry) {
        originalRequest._fallbackRetry = true;
        currentApiUrl = API_FALLBACK_URL;
        api.defaults.baseURL = currentApiUrl;

        console.log('Switching to fallback API:', currentApiUrl);
        return api(originalRequest);
      }
    }

    // Don't try to refresh token for logout requests
    if (error.response?.status === 401 && !originalRequest._retry && !originalRequest.url?.includes('/auth/logout/')) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await api.post('/auth/refresh/', {
            refresh: refreshToken,
          });

          const { access } = response.data;
          localStorage.setItem('access_token', access);

          // Retry the original request
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (data: LoginData | { username: string; password: string }): Promise<TokenResponse> => {
    let loginPayload: { username: string; password: string };

    // Handle both LoginData (with email) and direct username/password objects
    if ('email' in data) {
      // LoginData format: { email, password }
      if (!data.email || typeof data.email !== 'string') {
        throw new Error('البريد الإلكتروني مطلوب');
      }

      let username = data.email;

      // Map specific emails to usernames
      if (data.email === '<EMAIL>') {
        username = 'founder';
      } else if (data.email === '<EMAIL>') {
        username = 'admin';
      } else {
        // For other emails, extract username from email
        username = data.email.split('@')[0];
      }

      loginPayload = {
        username: username,
        password: data.password
      };
    } else {
      // Direct username/password format: { username, password }
      if (!data.username || typeof data.username !== 'string') {
        throw new Error('اسم المستخدم مطلوب');
      }
      loginPayload = {
        username: data.username,
        password: data.password
      };
    }

    if (!loginPayload.password || typeof loginPayload.password !== 'string') {
      throw new Error('كلمة المرور مطلوبة');
    }

    console.log('Login attempt:', {
      originalData: 'email' in data ? { email: data.email } : { username: data.username },
      loginPayload: { username: loginPayload.username }
    });

    const response = await api.post('/auth/login/', loginPayload);
    return response.data;
  },

  register: async (data: RegisterData): Promise<TokenResponse> => {
    const response = await api.post('/auth/register/', data);
    return response.data;
  },

  logout: async (): Promise<void> => {
    try {
      // Get refresh token to send for blacklisting
      const refreshToken = localStorage.getItem('refresh_token');

      // Send refresh token to backend for blacklisting
      if (refreshToken) {
        await api.post('/auth/logout/', {
          refresh: refreshToken,
        });
      } else {
        // If no refresh token, still call the endpoint
        await api.post('/auth/logout/', {});
      }
    } catch (error) {
      // Log the error but don't throw it - we still want to clear local storage
      console.error('Logout API error:', error);
    } finally {
      // Always clear local storage regardless of API call success
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  },

  getProfile: async (): Promise<User> => {
    const response = await api.get('/auth/profile/');
    return response.data;
  },

  refreshToken: async (refreshToken: string): Promise<{ access: string }> => {
    const response = await api.post('/auth/refresh/', {
      refresh: refreshToken,
    });
    return response.data;
  },
};

// Users API
export const usersAPI = {
  getUsers: async (params?: any) => {
    const response = await api.get('/auth/users/', { params });
    return response.data;
  },

  getUser: async (id: string): Promise<User> => {
    const response = await api.get(`/auth/users/${id}/`);
    return response.data;
  },

  updateUser: async (id: string, data: Partial<User>): Promise<User> => {
    const response = await api.patch(`/auth/users/${id}/`, data);
    return response.data;
  },

  deleteUser: async (id: string): Promise<void> => {
    await api.delete(`/auth/users/${id}/`);
  },
};

// Clients API
export const clientsAPI = {
  getClients: async (params?: any) => {
    const response = await api.get('/clients/', { params });
    return response.data;
  },

  getClient: async (id: string) => {
    const response = await api.get(`/clients/${id}/`);
    return response.data;
  },

  createClient: async (data: any) => {
    const response = await api.post('/clients/', data);
    return response.data;
  },

  updateClient: async (id: string, data: any) => {
    const response = await api.patch(`/clients/${id}/`, data);
    return response.data;
  },

  deleteClient: async (id: string): Promise<void> => {
    await api.delete(`/clients/${id}/`);
  },
};

// Projects API
export const projectsAPI = {
  getProjects: async (params?: any) => {
    const response = await api.get('/projects/', { params });
    return response.data;
  },

  getProject: async (id: string) => {
    const response = await api.get(`/projects/${id}/`);
    return response.data;
  },

  createProject: async (data: any) => {
    const response = await api.post('/projects/', data);
    return response.data;
  },

  updateProject: async (id: string, data: any) => {
    const response = await api.patch(`/projects/${id}/`, data);
    return response.data;
  },

  deleteProject: async (id: string): Promise<void> => {
    await api.delete(`/projects/${id}/`);
  },

  createProjectWithClient: async (data: any) => {
    const response = await api.post('/projects/create_with_client/', data);
    return response.data;
  },
};

// Tasks API
export const tasksAPI = {
  getTasks: async (params?: any) => {
    const response = await api.get('/tasks/', { params });
    return response.data;
  },

  getTask: async (id: string) => {
    const response = await api.get(`/tasks/${id}/`);
    return response.data;
  },

  createTask: async (data: any) => {
    const response = await api.post('/tasks/', data);
    return response.data;
  },

  updateTask: async (id: string, data: any) => {
    const response = await api.patch(`/tasks/${id}/`, data);
    return response.data;
  },

  deleteTask: async (id: string): Promise<void> => {
    await api.delete(`/tasks/${id}/`);
  },

  logTime: async (taskId: string, data: any) => {
    const response = await api.post(`/tasks/${taskId}/time-logs/`, data);
    return response.data;
  },

  addComment: async (taskId: string, data: any) => {
    const response = await api.post(`/tasks/${taskId}/comments/`, data);
    return response.data;
  },
};

// Team API
export const teamAPI = {
  getTeamMembers: async (params?: any) => {
    const response = await api.get('/team/', { params });
    return response.data;
  },

  getTeamMember: async (id: string) => {
    const response = await api.get(`/team/${id}/`);
    return response.data;
  },

  createTeamMember: async (data: any) => {
    const response = await api.post('/team/', data);
    return response.data;
  },

  updateTeamMember: async (id: string, data: any) => {
    const response = await api.patch(`/team/${id}/`, data);
    return response.data;
  },

  deleteTeamMember: async (id: string): Promise<void> => {
    await api.delete(`/team/${id}/`);
  },

  getTeamStats: async () => {
    const response = await api.get('/team/stats/');
    return response.data;
  },

  // Department-specific endpoints
  getDepartmentMembers: async (department: string, params?: any) => {
    const response = await api.get(`/team/departments/${department}/`, { params });
    return response.data;
  },

  getDepartmentStats: async (department: string) => {
    const response = await api.get(`/team/departments/${department}/stats/`);
    return response.data;
  },

  getDepartments: async () => {
    const response = await api.get('/team/departments/');
    return response.data;
  },

  updatePerformance: async (id: string, data: any) => {
    const response = await api.post(`/team/${id}/update_performance/`, data);
    return response.data;
  },

  getPerformanceHistory: async (id: string) => {
    const response = await api.get(`/team/${id}/performance_history/`);
    return response.data;
  },

  getDepartments: async () => {
    const response = await api.get('/team/departments/');
    return response.data;
  },

  getRecentHires: async () => {
    const response = await api.get('/team/recent_hires/');
    return response.data;
  },
};

// Finance API
export const financeAPI = {
  // Revenue Management
  getRevenues: async (params?: any) => {
    const response = await api.get('/revenue/', { params });
    return response.data;
  },

  getRevenue: async (id: string) => {
    const response = await api.get(`/revenue/${id}/`);
    return response.data;
  },

  createRevenue: async (data: any) => {
    const response = await api.post('/revenue/', data);
    return response.data;
  },

  updateRevenue: async (id: string, data: any) => {
    const response = await api.patch(`/revenue/${id}/`, data);
    return response.data;
  },

  deleteRevenue: async (id: string): Promise<void> => {
    await api.delete(`/revenue/${id}/`);
  },

  getRevenueSummary: async () => {
    const response = await api.get('/revenue/summary/');
    return response.data;
  },

  getOverdueRevenues: async () => {
    const response = await api.get('/revenue/overdue/');
    return response.data;
  },

  // Expense Management
  getExpenses: async (params?: any) => {
    const response = await api.get('/expenses/', { params });
    return response.data;
  },

  getExpense: async (id: string) => {
    const response = await api.get(`/expenses/${id}/`);
    return response.data;
  },

  createExpense: async (data: any) => {
    const response = await api.post('/expenses/', data);
    return response.data;
  },

  updateExpense: async (id: string, data: any) => {
    const response = await api.patch(`/expenses/${id}/`, data);
    return response.data;
  },

  deleteExpense: async (id: string): Promise<void> => {
    await api.delete(`/expenses/${id}/`);
  },

  getExpenseSummary: async () => {
    const response = await api.get('/expenses/summary/');
    return response.data;
  },

  approveExpense: async (id: string) => {
    const response = await api.post(`/expenses/${id}/approve/`);
    return response.data;
  },

  rejectExpense: async (id: string) => {
    const response = await api.post(`/expenses/${id}/reject/`);
    return response.data;
  },

  // Expense Categories
  getExpenseCategories: async () => {
    const response = await api.get('/expense-categories/');
    return response.data;
  },

  getExpenseCategoryTree: async () => {
    const response = await api.get('/expense-categories/tree/');
    return response.data;
  },

  createExpenseCategory: async (data: any) => {
    const response = await api.post('/expense-categories/', data);
    return response.data;
  },

  // Cash Flow Analysis
  getCashFlowProjections: async (params?: any) => {
    const response = await api.get('/cash-flow/', { params });
    return response.data;
  },

  getCashFlowProjection: async (id: string) => {
    const response = await api.get(`/cash-flow/${id}/`);
    return response.data;
  },

  createCashFlowProjection: async (data: any) => {
    const response = await api.post('/cash-flow/', data);
    return response.data;
  },

  updateCashFlowProjection: async (id: string, data: any) => {
    const response = await api.patch(`/cash-flow/${id}/`, data);
    return response.data;
  },

  getCurrentYearCashFlow: async () => {
    const response = await api.get('/cash-flow/current_year/');
    return response.data;
  },

  autoUpdateCashFlow: async () => {
    const response = await api.post('/cash-flow/auto_update/');
    return response.data;
  },

  getCashFlowSummary: async () => {
    const response = await api.get('/cash-flow/summary/');
    return response.data;
  },

  // Budget Planning
  getBudgets: async (params?: any) => {
    const response = await api.get('/budgets/', { params });
    return response.data;
  },

  getBudget: async (id: string) => {
    const response = await api.get(`/budgets/${id}/`);
    return response.data;
  },

  createBudget: async (data: any) => {
    const response = await api.post('/budgets/', data);
    return response.data;
  },

  updateBudget: async (id: string, data: any) => {
    const response = await api.patch(`/budgets/${id}/`, data);
    return response.data;
  },

  deleteBudget: async (id: string): Promise<void> => {
    await api.delete(`/budgets/${id}/`);
  },

  activateBudget: async (id: string) => {
    const response = await api.post(`/budgets/${id}/activate/`);
    return response.data;
  },

  getBudgetSummary: async () => {
    const response = await api.get('/budgets/summary/');
    return response.data;
  },

  // Financial KPIs
  getKPIs: async (params?: any) => {
    const response = await api.get('/kpis/', { params });
    return response.data;
  },

  getKPI: async (id: string) => {
    const response = await api.get(`/kpis/${id}/`);
    return response.data;
  },

  createKPI: async (data: any) => {
    const response = await api.post('/kpis/', data);
    return response.data;
  },

  updateKPI: async (id: string, data: any) => {
    const response = await api.patch(`/kpis/${id}/`, data);
    return response.data;
  },

  getKPIDashboard: async () => {
    const response = await api.get('/kpis/dashboard/');
    return response.data;
  },

  autoCalculateKPIs: async (data: any) => {
    const response = await api.post('/kpis/auto_calculate/', data);
    return response.data;
  },

  // Financial Dashboard
  getFinancialOverview: async () => {
    const response = await api.get('/dashboard/overview/');
    return response.data;
  },

  getDepartmentAnalysis: async () => {
    const response = await api.get('/dashboard/department_analysis/');
    return response.data;
  },

  getFinancialTrends: async (months?: number) => {
    const params = months ? { months } : {};
    const response = await api.get('/dashboard/trends/', { params });
    return response.data;
  },

  // Financial Reports
  getFinancialReports: async (params?: any) => {
    const response = await api.get('/reports/', { params });
    return response.data;
  },

  getFinancialReport: async (id: string) => {
    const response = await api.get(`/reports/${id}/`);
    return response.data;
  },

  generateReport: async (data: any) => {
    const response = await api.post('/reports/generate/', data);
    return response.data;
  },

  downloadReport: async (id: string) => {
    const response = await api.get(`/reports/${id}/download/`, {
      responseType: 'blob'
    });
    return response.data;
  },

  getReportsSummary: async () => {
    const response = await api.get('/reports/summary/');
    return response.data;
  },
};

export default api;
