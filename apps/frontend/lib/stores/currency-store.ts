import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { CURRENCY_CONFIG } from '@mtbrmg/shared';
import { currencyConversionService, type ConversionResult, type CurrencyData } from '../services/currency-conversion';

interface CurrencyState {
  currentCurrency: string;
  baseCurrency: string;
  exchangeRates: CurrencyData | null;
  isConverting: boolean;
  lastConversionUpdate: string | null;
  conversionSource: 'api' | 'cache' | 'fallback' | 'manual' | 'default' | null;

  // Basic currency functions
  setCurrency: (currency: string) => void;
  getCurrencySymbol: (currency?: string) => string;
  getCurrencyLabel: (currency?: string) => string;
  formatCurrency: (amount: number, currency?: string) => string;

  // Conversion functions
  convertAmount: (amount: number, fromCurrency?: string, toCurrency?: string) => Promise<ConversionResult>;
  formatCurrencyWithConversion: (amount: number, fromCurrency?: string, toCurrency?: string) => Promise<string>;
  refreshExchangeRates: (forceRefresh?: boolean) => Promise<void>;

  // Manual exchange rate management
  updateExchangeRate: (currency: string, egpValue: number) => void;
  getManualExchangeRate: (currency: string) => number;
  resetToDefaultRates: () => void;

  // Utility functions
  isConversionEnabled: () => boolean;
  getConversionStatus: () => { isConverted: boolean; source: string; lastUpdate: string | null };
}

export const useCurrencyStore = create<CurrencyState>()(
  persist(
    (set, get) => ({
      currentCurrency: CURRENCY_CONFIG.PRIMARY,
      baseCurrency: 'EGP',
      exchangeRates: currencyConversionService.getExchangeRatesImmediate(), // Initialize immediately with fallback
      isConverting: false,
      lastConversionUpdate: null,
      conversionSource: 'fallback',

      setCurrency: (currency: string) => {
        if (CURRENCY_CONFIG.SUPPORTED.includes(currency)) {
          console.log('CurrencyStore: Setting currency to', currency);
          set({ currentCurrency: currency });

          // Refresh exchange rates in background when currency changes (non-blocking)
          if (currency !== get().baseCurrency) {
            get().refreshExchangeRates(false).catch(error => {
              console.error('CurrencyStore: Background rate refresh failed:', error);
            });
          }
        }
      },

      getCurrencySymbol: (currency?: string) => {
        const curr = currency || get().currentCurrency;
        return CURRENCY_CONFIG.SYMBOLS[curr as keyof typeof CURRENCY_CONFIG.SYMBOLS] || curr;
      },

      getCurrencyLabel: (currency?: string) => {
        const curr = currency || get().currentCurrency;
        return CURRENCY_CONFIG.LABELS[curr as keyof typeof CURRENCY_CONFIG.LABELS] || curr;
      },

      formatCurrency: (amount: number, currency?: string) => {
        const curr = currency || get().currentCurrency;

        // Use appropriate locale based on currency
        let locale = 'ar-EG'; // Default to Arabic-Egypt

        switch (curr) {
          case 'USD':
            locale = 'en-US';
            break;
          case 'SAR':
            locale = 'ar-SA';
            break;
          case 'AED':
            locale = 'ar-AE';
            break;
          case 'EGP':
          default:
            locale = 'ar-EG';
            break;
        }

        const formatter = new Intl.NumberFormat(locale, {
          style: 'currency',
          currency: curr,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });

        return formatter.format(amount);
      },

      // Conversion functions
      convertAmount: async (amount: number, fromCurrency?: string, toCurrency?: string) => {
        const from = fromCurrency || get().baseCurrency;
        const to = toCurrency || get().currentCurrency;

        set({ isConverting: true });

        try {
          const result = await currencyConversionService.convertCurrency(amount, from, to);

          set({
            isConverting: false,
            lastConversionUpdate: result.timestamp,
            conversionSource: result.source
          });

          return result;
        } catch (error) {
          console.error('Conversion error:', error);
          set({ isConverting: false });

          return {
            success: false,
            convertedAmount: amount,
            rate: 1,
            fromCurrency: from,
            toCurrency: to,
            timestamp: new Date().toISOString(),
            source: 'fallback' as const
          };
        }
      },

      formatCurrencyWithConversion: async (amount: number, fromCurrency?: string, toCurrency?: string) => {
        const conversion = await get().convertAmount(amount, fromCurrency, toCurrency);
        return get().formatCurrency(conversion.convertedAmount, conversion.toCurrency);
      },

      refreshExchangeRates: async (forceRefresh = false) => {
        console.log('CurrencyStore: Refreshing exchange rates (manual mode)', { forceRefresh });
        set({ isConverting: true });

        // API calls disabled - always use manual/default rates
        try {
          const rates = currencyConversionService.getExchangeRatesImmediate();
          console.log('CurrencyStore: Got manual/default rates', { source: rates.source });

          set({
            exchangeRates: rates,
            isConverting: false,
            lastConversionUpdate: rates.lastUpdate,
            conversionSource: rates.source as 'api' | 'cache' | 'fallback' | 'manual' | 'default'
          });
        } catch (error) {
          console.error('CurrencyStore: Failed to get exchange rates:', error);

          // Ensure we always have default rates
          const defaultRates = currencyConversionService.getExchangeRatesImmediate();
          set({
            exchangeRates: defaultRates,
            isConverting: false,
            lastConversionUpdate: defaultRates.lastUpdate,
            conversionSource: 'default'
          });
        }
      },

      // Manual exchange rate management
      updateExchangeRate: (currency: string, egpValue: number) => {
        console.log('CurrencyStore: Updating manual exchange rate', { currency, egpValue });

        // Set the manual rate
        currencyConversionService.setExchangeRateFromEGP(currency, egpValue);

        // Refresh the store with new rates
        const updatedRates = currencyConversionService.getExchangeRatesImmediate();
        set({
          exchangeRates: updatedRates,
          lastConversionUpdate: updatedRates.lastUpdate,
          conversionSource: 'manual'
        });
      },

      getManualExchangeRate: (currency: string): number => {
        return currencyConversionService.getEGPValueForCurrency(currency);
      },

      resetToDefaultRates: () => {
        console.log('CurrencyStore: Resetting to default rates');

        // Clear manual rates
        currencyConversionService.resetToDefaultRates();

        // Refresh the store with default rates
        const defaultRates = currencyConversionService.getExchangeRatesImmediate();
        set({
          exchangeRates: defaultRates,
          lastConversionUpdate: defaultRates.lastUpdate,
          conversionSource: 'default'
        });
      },

      isConversionEnabled: () => {
        const { currentCurrency, baseCurrency } = get();
        return currentCurrency !== baseCurrency;
      },

      getConversionStatus: () => {
        const { currentCurrency, baseCurrency, conversionSource, lastConversionUpdate } = get();
        return {
          isConverted: currentCurrency !== baseCurrency,
          source: conversionSource || 'none',
          lastUpdate: lastConversionUpdate
        };
      },
    }),
    {
      name: 'mtbrmg-currency-store',
      skipHydration: true,
    }
  )
);

// Background initialization function (non-blocking)
export const initializeCurrencyStore = () => {
  console.log('CurrencyStore: Starting background initialization');

  // Initialize with immediate fallback rates
  const store = useCurrencyStore.getState();
  if (!store.exchangeRates) {
    const fallbackRates = currencyConversionService.getExchangeRatesImmediate();
    useCurrencyStore.setState({
      exchangeRates: fallbackRates,
      lastConversionUpdate: fallbackRates.lastUpdate,
      conversionSource: fallbackRates.source as 'api' | 'cache' | 'fallback'
    });
  }

  // Try to refresh rates in background (non-blocking)
  setTimeout(() => {
    store.refreshExchangeRates(false).catch(error => {
      console.error('CurrencyStore: Background initialization failed:', error);
    });
  }, 1000); // Delay to not block app startup
};

// Enhanced hook for easy access to currency functions
export const useCurrency = () => {
  const store = useCurrencyStore();

  return {
    // Basic properties
    currentCurrency: store.currentCurrency,
    baseCurrency: store.baseCurrency,
    isConverting: store.isConverting,

    // Basic functions
    setCurrency: store.setCurrency,
    symbol: store.getCurrencySymbol(),
    label: store.getCurrencyLabel(),
    format: store.formatCurrency,
    getSymbol: store.getCurrencySymbol,
    getLabel: store.getCurrencyLabel,

    // Conversion functions
    convertAmount: store.convertAmount,
    formatWithConversion: store.formatCurrencyWithConversion,
    refreshRates: store.refreshExchangeRates,

    // Manual rate management
    updateExchangeRate: store.updateExchangeRate,
    getManualExchangeRate: store.getManualExchangeRate,
    resetToDefaultRates: store.resetToDefaultRates,

    // Status functions
    isConversionEnabled: store.isConversionEnabled(),
    conversionStatus: store.getConversionStatus(),
  };
};
