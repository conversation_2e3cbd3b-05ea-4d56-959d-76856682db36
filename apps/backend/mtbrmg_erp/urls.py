"""
URL configuration for mtbrmg_erp project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse

def health_check(request):
    """Simple health check endpoint"""
    return JsonResponse({
        'status': 'healthy',
        'service': 'mtbrmg-erp-backend',
        'version': '1.0.0'
    })

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/health/", health_check, name='health_check'),

    # API v1 endpoints
    path("api/v1/auth/", include('authentication.urls')),
    path("api/v1/", include('clients.urls')),
    path("api/v1/", include('projects.urls')),
    path("api/v1/", include('tasks.urls')),
    path("api/v1/", include('team.urls')),
    path("api/v1/", include('finance.urls')),
    path("api/v1/", include('commissions.urls')),

    # Legacy API endpoints (for backward compatibility)
    path("api/auth/", include('authentication.urls')),
    path("api/", include('clients.urls')),
    path("api/", include('projects.urls')),
    path("api/", include('tasks.urls')),
    path("api/", include('team.urls')),
    path("api/", include('finance.urls')),
    path("api/", include('commissions.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
