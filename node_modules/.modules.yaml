hoistPattern:
  - '*'
hoistedDependencies:
  /@alloc/quick-lru/5.2.0:
    '@alloc/quick-lru': private
  /@babel/runtime-corejs3/7.27.4:
    '@babel/runtime-corejs3': private
  /@babel/runtime/7.27.4:
    '@babel/runtime': private
  /@changesets/apply-release-plan/7.0.12:
    '@changesets/apply-release-plan': private
  /@changesets/assemble-release-plan/6.0.8:
    '@changesets/assemble-release-plan': private
  /@changesets/changelog-git/0.2.1:
    '@changesets/changelog-git': private
  /@changesets/config/3.1.1:
    '@changesets/config': private
  /@changesets/errors/0.2.0:
    '@changesets/errors': private
  /@changesets/get-dependents-graph/2.1.3:
    '@changesets/get-dependents-graph': private
  /@changesets/get-release-plan/4.0.12:
    '@changesets/get-release-plan': private
  /@changesets/get-version-range-type/0.4.0:
    '@changesets/get-version-range-type': private
  /@changesets/git/3.0.4:
    '@changesets/git': private
  /@changesets/logger/0.1.1:
    '@changesets/logger': private
  /@changesets/parse/0.4.1:
    '@changesets/parse': private
  /@changesets/pre/2.0.2:
    '@changesets/pre': private
  /@changesets/read/0.6.5:
    '@changesets/read': private
  /@changesets/should-skip-package/0.1.2:
    '@changesets/should-skip-package': private
  /@changesets/types/6.1.0:
    '@changesets/types': private
  /@changesets/write/0.4.0:
    '@changesets/write': private
  /@cspotcode/source-map-support/0.8.1:
    '@cspotcode/source-map-support': private
  /@emotion/is-prop-valid/0.8.8:
    '@emotion/is-prop-valid': private
  /@emotion/memoize/0.7.4:
    '@emotion/memoize': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.57.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.57.1:
    '@eslint/js': public
  /@floating-ui/core/1.7.0:
    '@floating-ui/core': private
  /@floating-ui/dom/1.7.0:
    '@floating-ui/dom': private
  /@floating-ui/react-dom/2.1.2(react-dom@19.1.0)(react@19.1.0):
    '@floating-ui/react-dom': private
  /@floating-ui/utils/0.2.9:
    '@floating-ui/utils': private
  /@fontsource/ibm-plex-sans-arabic/5.2.5:
    '@fontsource/ibm-plex-sans-arabic': private
  /@formatjs/ecma402-abstract/2.3.4:
    '@formatjs/ecma402-abstract': private
  /@formatjs/fast-memoize/2.2.7:
    '@formatjs/fast-memoize': private
  /@formatjs/icu-messageformat-parser/2.11.2:
    '@formatjs/icu-messageformat-parser': private
  /@formatjs/icu-skeleton-parser/1.8.14:
    '@formatjs/icu-skeleton-parser': private
  /@formatjs/intl-localematcher/0.5.10:
    '@formatjs/intl-localematcher': private
  /@hookform/resolvers/3.10.0(react-hook-form@7.56.4):
    '@hookform/resolvers': private
  /@humanwhocodes/config-array/0.13.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@img/sharp-darwin-arm64/0.33.5:
    '@img/sharp-darwin-arm64': private
  /@img/sharp-darwin-x64/0.33.5:
    '@img/sharp-darwin-x64': private
  /@img/sharp-libvips-darwin-arm64/1.0.4:
    '@img/sharp-libvips-darwin-arm64': private
  /@img/sharp-libvips-darwin-x64/1.0.4:
    '@img/sharp-libvips-darwin-x64': private
  /@img/sharp-libvips-linux-arm/1.0.5:
    '@img/sharp-libvips-linux-arm': private
  /@img/sharp-libvips-linux-arm64/1.0.4:
    '@img/sharp-libvips-linux-arm64': private
  /@img/sharp-libvips-linux-s390x/1.0.4:
    '@img/sharp-libvips-linux-s390x': private
  /@img/sharp-libvips-linux-x64/1.0.4:
    '@img/sharp-libvips-linux-x64': private
  /@img/sharp-libvips-linuxmusl-arm64/1.0.4:
    '@img/sharp-libvips-linuxmusl-arm64': private
  /@img/sharp-libvips-linuxmusl-x64/1.0.4:
    '@img/sharp-libvips-linuxmusl-x64': private
  /@img/sharp-linux-arm/0.33.5:
    '@img/sharp-linux-arm': private
  /@img/sharp-linux-arm64/0.33.5:
    '@img/sharp-linux-arm64': private
  /@img/sharp-linux-s390x/0.33.5:
    '@img/sharp-linux-s390x': private
  /@img/sharp-linux-x64/0.33.5:
    '@img/sharp-linux-x64': private
  /@img/sharp-linuxmusl-arm64/0.33.5:
    '@img/sharp-linuxmusl-arm64': private
  /@img/sharp-linuxmusl-x64/0.33.5:
    '@img/sharp-linuxmusl-x64': private
  /@img/sharp-wasm32/0.33.5:
    '@img/sharp-wasm32': private
  /@img/sharp-win32-ia32/0.33.5:
    '@img/sharp-win32-ia32': private
  /@img/sharp-win32-x64/0.33.5:
    '@img/sharp-win32-x64': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@jridgewell/gen-mapping/0.3.8:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  /@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.9:
    '@jridgewell/trace-mapping': private
  /@manypkg/find-root/1.1.0:
    '@manypkg/find-root': private
  /@manypkg/get-packages/1.1.3:
    '@manypkg/get-packages': private
  /@next/env/15.2.4:
    '@next/env': private
  /@next/swc-darwin-arm64/15.2.4:
    '@next/swc-darwin-arm64': private
  /@next/swc-darwin-x64/15.2.4:
    '@next/swc-darwin-x64': private
  /@next/swc-linux-arm64-gnu/15.2.4:
    '@next/swc-linux-arm64-gnu': private
  /@next/swc-linux-arm64-musl/15.2.4:
    '@next/swc-linux-arm64-musl': private
  /@next/swc-linux-x64-gnu/15.2.4:
    '@next/swc-linux-x64-gnu': private
  /@next/swc-linux-x64-musl/15.2.4:
    '@next/swc-linux-x64-musl': private
  /@next/swc-win32-arm64-msvc/15.2.4:
    '@next/swc-win32-arm64-msvc': private
  /@next/swc-win32-x64-msvc/15.2.4:
    '@next/swc-win32-x64-msvc': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@radix-ui/number/1.1.0:
    '@radix-ui/number': private
  /@radix-ui/primitive/1.1.1:
    '@radix-ui/primitive': private
  /@radix-ui/react-accordion/1.2.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-accordion': private
  /@radix-ui/react-alert-dialog/1.1.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-alert-dialog': private
  /@radix-ui/react-arrow/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-arrow': private
  /@radix-ui/react-aspect-ratio/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-aspect-ratio': private
  /@radix-ui/react-avatar/1.1.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-avatar': private
  /@radix-ui/react-checkbox/1.1.3(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-checkbox': private
  /@radix-ui/react-collapsible/1.1.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-collapsible': private
  /@radix-ui/react-collection/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-collection': private
  /@radix-ui/react-compose-refs/1.1.1(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-compose-refs': private
  /@radix-ui/react-context-menu/2.2.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-context-menu': private
  /@radix-ui/react-context/1.1.1(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-context': private
  /@radix-ui/react-dialog/1.1.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-dialog': private
  /@radix-ui/react-direction/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-direction': private
  /@radix-ui/react-dismissable-layer/1.1.3(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-dismissable-layer': private
  /@radix-ui/react-dropdown-menu/2.1.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-dropdown-menu': private
  /@radix-ui/react-focus-guards/1.1.1(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-focus-guards': private
  /@radix-ui/react-focus-scope/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-focus-scope': private
  /@radix-ui/react-hover-card/1.1.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-hover-card': private
  /@radix-ui/react-id/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-id': private
  /@radix-ui/react-label/2.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-label': private
  /@radix-ui/react-menu/2.1.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-menu': private
  /@radix-ui/react-menubar/1.1.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-menubar': private
  /@radix-ui/react-navigation-menu/1.2.3(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-navigation-menu': private
  /@radix-ui/react-popover/1.1.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-popover': private
  /@radix-ui/react-popper/1.2.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-popper': private
  /@radix-ui/react-portal/1.1.3(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-portal': private
  /@radix-ui/react-presence/1.1.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-presence': private
  /@radix-ui/react-primitive/2.0.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-primitive': private
  /@radix-ui/react-progress/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-progress': private
  /@radix-ui/react-radio-group/1.2.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-radio-group': private
  /@radix-ui/react-roving-focus/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-roving-focus': private
  /@radix-ui/react-scroll-area/1.2.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-scroll-area': private
  /@radix-ui/react-select/2.1.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-select': private
  /@radix-ui/react-separator/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-separator': private
  /@radix-ui/react-slider/1.2.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-slider': private
  /@radix-ui/react-slot/1.1.1(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-slot': private
  /@radix-ui/react-switch/1.1.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-switch': private
  /@radix-ui/react-tabs/1.1.2(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-tabs': private
  /@radix-ui/react-toast/1.2.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-toast': private
  /@radix-ui/react-toggle-group/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-toggle-group': private
  /@radix-ui/react-toggle/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-toggle': private
  /@radix-ui/react-tooltip/1.1.6(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-tooltip': private
  /@radix-ui/react-use-callback-ref/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-use-callback-ref': private
  /@radix-ui/react-use-controllable-state/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-use-controllable-state': private
  /@radix-ui/react-use-escape-keydown/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-use-escape-keydown': private
  /@radix-ui/react-use-layout-effect/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-use-layout-effect': private
  /@radix-ui/react-use-previous/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-use-previous': private
  /@radix-ui/react-use-rect/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-use-rect': private
  /@radix-ui/react-use-size/1.1.0(@types/react@19.1.6)(react@19.1.0):
    '@radix-ui/react-use-size': private
  /@radix-ui/react-visually-hidden/1.1.1(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-visually-hidden': private
  /@radix-ui/rect/1.1.0:
    '@radix-ui/rect': private
  /@swc/counter/0.1.3:
    '@swc/counter': private
  /@swc/helpers/0.5.15:
    '@swc/helpers': private
  /@tanstack/query-core/5.79.0:
    '@tanstack/query-core': private
  /@tanstack/query-devtools/5.76.0:
    '@tanstack/query-devtools': private
  /@tanstack/react-query-devtools/5.79.0(@tanstack/react-query@5.79.0)(react@19.1.0):
    '@tanstack/react-query-devtools': private
  /@tanstack/react-query/5.79.0(react@19.1.0):
    '@tanstack/react-query': private
  /@tanstack/react-table/8.21.3(react-dom@19.1.0)(react@19.1.0):
    '@tanstack/react-table': private
  /@tanstack/table-core/8.21.3:
    '@tanstack/table-core': private
  /@tootallnate/quickjs-emscripten/0.23.0:
    '@tootallnate/quickjs-emscripten': private
  /@tsconfig/node10/1.0.11:
    '@tsconfig/node10': private
  /@tsconfig/node12/1.0.11:
    '@tsconfig/node12': private
  /@tsconfig/node14/1.0.3:
    '@tsconfig/node14': private
  /@tsconfig/node16/1.0.4:
    '@tsconfig/node16': private
  /@turbo/workspaces/1.13.4:
    '@turbo/workspaces': private
  /@types/d3-array/3.2.1:
    '@types/d3-array': private
  /@types/d3-color/3.1.3:
    '@types/d3-color': private
  /@types/d3-ease/3.0.2:
    '@types/d3-ease': private
  /@types/d3-interpolate/3.0.4:
    '@types/d3-interpolate': private
  /@types/d3-path/3.1.1:
    '@types/d3-path': private
  /@types/d3-scale/4.0.9:
    '@types/d3-scale': private
  /@types/d3-shape/3.1.7:
    '@types/d3-shape': private
  /@types/d3-time/3.0.4:
    '@types/d3-time': private
  /@types/d3-timer/3.0.2:
    '@types/d3-timer': private
  /@types/glob/7.2.0:
    '@types/glob': private
  /@types/inquirer/6.5.0:
    '@types/inquirer': private
  /@types/minimatch/5.1.2:
    '@types/minimatch': private
  /@types/node/22.15.29:
    '@types/node': private
  /@types/react-dom/19.1.5(@types/react@19.1.6):
    '@types/react-dom': private
  /@types/react/19.1.6:
    '@types/react': private
  /@types/through/0.0.33:
    '@types/through': private
  /@types/tinycolor2/1.4.6:
    '@types/tinycolor2': private
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /acorn-jsx/5.3.2(acorn@8.14.1):
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.14.1:
    acorn: private
  /agent-base/7.1.3:
    agent-base: private
  /aggregate-error/3.1.0:
    aggregate-error: private
  /ajv/6.12.6:
    ajv: private
  /ansi-colors/4.1.3:
    ansi-colors: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/3.2.1:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arg/5.0.2:
    arg: private
  /argparse/2.0.1:
    argparse: private
  /aria-hidden/1.2.6:
    aria-hidden: private
  /array-union/2.1.0:
    array-union: private
  /ast-types/0.13.4:
    ast-types: private
  /asynckit/0.4.0:
    asynckit: private
  /autoprefixer/10.4.21(postcss@8.5.4):
    autoprefixer: private
  /axios/1.9.0:
    axios: private
  /balanced-match/1.0.2:
    balanced-match: private
  /base64-js/1.5.1:
    base64-js: private
  /basic-ftp/5.0.5:
    basic-ftp: private
  /better-path-resolve/1.0.0:
    better-path-resolve: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /bl/4.1.0:
    bl: private
  /brace-expansion/1.1.11:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.25.0:
    browserslist: private
  /buffer/5.7.1:
    buffer: private
  /busboy/1.6.0:
    busboy: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /callsites/3.1.0:
    callsites: private
  /camel-case/3.0.0:
    camel-case: private
  /camelcase-css/2.0.1:
    camelcase-css: private
  /caniuse-lite/1.0.30001720:
    caniuse-lite: private
  /chalk/2.4.2:
    chalk: private
  /change-case/3.1.0:
    change-case: private
  /chardet/0.7.0:
    chardet: private
  /chokidar/3.6.0:
    chokidar: private
  /ci-info/3.9.0:
    ci-info: private
  /class-variance-authority/0.7.1:
    class-variance-authority: private
  /clean-stack/2.2.0:
    clean-stack: private
  /cli-cursor/3.1.0:
    cli-cursor: private
  /cli-spinners/2.9.2:
    cli-spinners: private
  /cli-width/3.0.0:
    cli-width: private
  /client-only/0.0.1:
    client-only: private
  /clone/1.0.4:
    clone: private
  /clsx/2.1.1:
    clsx: private
  /cmdk/1.0.4(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    cmdk: private
  /color-convert/1.9.3:
    color-convert: private
  /color-name/1.1.3:
    color-name: private
  /color-string/1.9.1:
    color-string: private
  /color/4.2.3:
    color: private
  /combined-stream/1.0.8:
    combined-stream: private
  /commander/10.0.1:
    commander: private
  /concat-map/0.0.1:
    concat-map: private
  /constant-case/2.0.0:
    constant-case: private
  /core-js-pure/3.42.0:
    core-js-pure: private
  /create-require/1.1.1:
    create-require: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /cssesc/3.0.0:
    cssesc: private
  /csstype/3.1.3:
    csstype: private
  /d3-array/3.2.4:
    d3-array: private
  /d3-color/3.1.0:
    d3-color: private
  /d3-ease/3.0.1:
    d3-ease: private
  /d3-format/3.1.0:
    d3-format: private
  /d3-interpolate/3.0.1:
    d3-interpolate: private
  /d3-path/3.1.0:
    d3-path: private
  /d3-scale/4.0.2:
    d3-scale: private
  /d3-shape/3.2.0:
    d3-shape: private
  /d3-time-format/4.1.0:
    d3-time-format: private
  /d3-time/3.1.0:
    d3-time: private
  /d3-timer/3.0.1:
    d3-timer: private
  /data-uri-to-buffer/6.0.2:
    data-uri-to-buffer: private
  /date-fns/4.1.0:
    date-fns: private
  /debug/4.4.1:
    debug: private
  /decimal.js-light/2.5.1:
    decimal.js-light: private
  /decimal.js/10.5.0:
    decimal.js: private
  /deep-extend/0.6.0:
    deep-extend: private
  /deep-is/0.1.4:
    deep-is: private
  /defaults/1.0.4:
    defaults: private
  /degenerator/5.0.1:
    degenerator: private
  /del/5.1.0:
    del: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /detect-indent/6.1.0:
    detect-indent: private
  /detect-libc/2.0.4:
    detect-libc: private
  /detect-node-es/1.1.0:
    detect-node-es: private
  /didyoumean/1.2.2:
    didyoumean: private
  /diff/4.0.2:
    diff: private
  /dir-glob/3.0.1:
    dir-glob: private
  /dlv/1.1.3:
    dlv: private
  /doctrine/3.0.0:
    doctrine: private
  /dom-helpers/5.2.1:
    dom-helpers: private
  /dot-case/2.1.1:
    dot-case: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /electron-to-chromium/1.5.161:
    electron-to-chromium: private
  /embla-carousel-react/8.5.1(react@19.1.0):
    embla-carousel-react: private
  /embla-carousel-reactive-utils/8.5.1(embla-carousel@8.5.1):
    embla-carousel-reactive-utils: private
  /embla-carousel/8.5.1:
    embla-carousel: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /enquirer/2.4.1:
    enquirer: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /escalade/3.2.0:
    escalade: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /escodegen/2.1.0:
    escodegen: private
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /eslint/8.57.1:
    eslint: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /esutils/2.0.3:
    esutils: private
  /eventemitter3/4.0.7:
    eventemitter3: private
  /execa/5.1.1:
    execa: private
  /extendable-error/0.1.7:
    extendable-error: private
  /external-editor/3.1.0:
    external-editor: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-equals/5.2.2:
    fast-equals: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fastq/1.19.1:
    fastq: private
  /figures/3.2.0:
    figures: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /fill-range/7.1.1:
    fill-range: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /follow-redirects/1.15.9:
    follow-redirects: private
  /foreground-child/3.3.1:
    foreground-child: private
  /form-data/4.0.2:
    form-data: private
  /fraction.js/4.3.7:
    fraction.js: private
  /framer-motion/10.18.0(react-dom@19.1.0)(react@19.1.0):
    framer-motion: private
  /fs-extra/7.0.1:
    fs-extra: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-nonce/1.0.1:
    get-nonce: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/6.0.1:
    get-stream: private
  /get-uri/6.0.4:
    get-uri: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob/10.4.5:
    glob: private
  /globals/13.24.0:
    globals: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /gradient-string/2.0.2:
    gradient-string: private
  /graphemer/1.4.0:
    graphemer: private
  /handlebars/4.7.8:
    handlebars: private
  /has-flag/3.0.0:
    has-flag: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /header-case/1.0.1:
    header-case: private
  /http-proxy-agent/7.0.2:
    http-proxy-agent: private
  /https-proxy-agent/7.0.6:
    https-proxy-agent: private
  /human-id/4.1.1:
    human-id: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /ieee754/1.2.1:
    ieee754: private
  /ignore/5.3.2:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/1.3.8:
    ini: private
  /input-otp/1.4.1(react-dom@19.1.0)(react@19.1.0):
    input-otp: private
  /inquirer/8.2.6:
    inquirer: private
  /internmap/2.0.3:
    internmap: private
  /intl-messageformat/10.7.16:
    intl-messageformat: private
  /ip-address/9.0.5:
    ip-address: private
  /is-arrayish/0.3.2:
    is-arrayish: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-glob/4.0.3:
    is-glob: private
  /is-interactive/1.0.0:
    is-interactive: private
  /is-lower-case/1.1.3:
    is-lower-case: private
  /is-number/7.0.0:
    is-number: private
  /is-path-cwd/2.2.0:
    is-path-cwd: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-stream/2.0.1:
    is-stream: private
  /is-subdir/1.2.0:
    is-subdir: private
  /is-unicode-supported/0.1.0:
    is-unicode-supported: private
  /is-upper-case/1.1.2:
    is-upper-case: private
  /is-windows/1.0.2:
    is-windows: private
  /isbinaryfile/4.0.10:
    isbinaryfile: private
  /isexe/2.0.0:
    isexe: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jiti/1.21.7:
    jiti: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsbn/1.1.0:
    jsbn: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /jsonfile/6.1.0:
    jsonfile: private
  /keyv/4.5.4:
    keyv: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.get/4.4.2:
    lodash.get: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.startcase/4.4.0:
    lodash.startcase: private
  /lodash/4.17.21:
    lodash: private
  /log-symbols/3.0.0:
    log-symbols: private
  /loose-envify/1.4.0:
    loose-envify: private
  /lower-case-first/1.0.2:
    lower-case-first: private
  /lower-case/1.1.4:
    lower-case: private
  /lru-cache/7.18.3:
    lru-cache: private
  /lucide-react/0.454.0(react@19.1.0):
    lucide-react: private
  /make-error/1.3.6:
    make-error: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /micromatch/4.0.8:
    micromatch: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mimic-fn/2.1.0:
    mimic-fn: private
  /minimatch/9.0.5:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /mkdirp/0.5.6:
    mkdirp: private
  /mri/1.2.0:
    mri: private
  /ms/2.1.3:
    ms: private
  /mute-stream/0.0.8:
    mute-stream: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /natural-compare/1.4.0:
    natural-compare: private
  /negotiator/1.0.0:
    negotiator: private
  /neo-async/2.6.2:
    neo-async: private
  /netmask/2.0.2:
    netmask: private
  /next-intl/3.26.5(next@15.2.4)(react@19.1.0):
    next-intl: private
  /next-themes/0.4.6(react-dom@19.1.0)(react@19.1.0):
    next-themes: private
  /next/15.2.4(react-dom@19.1.0)(react@19.1.0):
    next: private
  /no-case/2.3.2:
    no-case: private
  /node-plop/0.26.3:
    node-plop: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /npm-run-path/4.0.1:
    npm-run-path: private
  /object-assign/4.1.1:
    object-assign: private
  /object-hash/3.0.0:
    object-hash: private
  /once/1.4.0:
    once: private
  /onetime/5.1.2:
    onetime: private
  /optionator/0.9.4:
    optionator: private
  /ora/4.1.1:
    ora: private
  /os-tmpdir/1.0.2:
    os-tmpdir: private
  /outdent/0.5.0:
    outdent: private
  /p-filter/2.1.0:
    p-filter: private
  /p-limit/2.3.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /p-map/3.0.0:
    p-map: private
  /p-try/2.2.0:
    p-try: private
  /pac-proxy-agent/7.2.0:
    pac-proxy-agent: private
  /pac-resolver/7.0.1:
    pac-resolver: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /package-manager-detector/0.2.11:
    package-manager-detector: private
  /param-case/2.1.1:
    param-case: private
  /parent-module/1.0.1:
    parent-module: private
  /pascal-case/2.0.1:
    pascal-case: private
  /path-case/2.1.1:
    path-case: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-type/4.0.0:
    path-type: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pify/2.3.0:
    pify: private
  /pirates/4.0.7:
    pirates: private
  /postcss-import/15.1.0(postcss@8.5.4):
    postcss-import: private
  /postcss-js/4.0.1(postcss@8.5.4):
    postcss-js: private
  /postcss-load-config/4.0.2(postcss@8.5.4):
    postcss-load-config: private
  /postcss-nested/6.2.0(postcss@8.5.4):
    postcss-nested: private
  /postcss-selector-parser/6.1.2:
    postcss-selector-parser: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /postcss/8.5.4:
    postcss: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prop-types/15.8.1:
    prop-types: private
  /proxy-agent/6.5.0:
    proxy-agent: private
  /proxy-from-env/1.1.0:
    proxy-from-env: private
  /punycode/2.3.1:
    punycode: private
  /quansync/0.2.10:
    quansync: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /rc/1.2.8:
    rc: private
  /react-day-picker/8.10.1(date-fns@4.1.0)(react@19.1.0):
    react-day-picker: private
  /react-dom/19.1.0(react@19.1.0):
    react-dom: private
  /react-hook-form/7.56.4(react@19.1.0):
    react-hook-form: private
  /react-is/18.3.1:
    react-is: private
  /react-remove-scroll-bar/2.3.8(@types/react@19.1.6)(react@19.1.0):
    react-remove-scroll-bar: private
  /react-remove-scroll/2.7.1(@types/react@19.1.6)(react@19.1.0):
    react-remove-scroll: private
  /react-resizable-panels/2.1.9(react-dom@19.1.0)(react@19.1.0):
    react-resizable-panels: private
  /react-smooth/4.0.4(react-dom@19.1.0)(react@19.1.0):
    react-smooth: private
  /react-style-singleton/2.2.3(@types/react@19.1.6)(react@19.1.0):
    react-style-singleton: private
  /react-transition-group/4.4.5(react-dom@19.1.0)(react@19.1.0):
    react-transition-group: private
  /react/19.1.0:
    react: private
  /read-cache/1.0.0:
    read-cache: private
  /read-yaml-file/1.1.0:
    read-yaml-file: private
  /readable-stream/3.6.2:
    readable-stream: private
  /readdirp/3.6.0:
    readdirp: private
  /recharts-scale/0.4.5:
    recharts-scale: private
  /recharts/2.15.0(react-dom@19.1.0)(react@19.1.0):
    recharts: private
  /registry-auth-token/3.3.2:
    registry-auth-token: private
  /registry-url/3.1.0:
    registry-url: private
  /resolve-from/5.0.0:
    resolve-from: private
  /resolve/1.22.10:
    resolve: private
  /restore-cursor/3.1.0:
    restore-cursor: private
  /reusify/1.1.0:
    reusify: private
  /rimraf/3.0.2:
    rimraf: private
  /run-async/2.4.1:
    run-async: private
  /run-parallel/1.2.0:
    run-parallel: private
  /rxjs/7.8.2:
    rxjs: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /scheduler/0.26.0:
    scheduler: private
  /semver/7.7.2:
    semver: private
  /sentence-case/2.1.1:
    sentence-case: private
  /sharp/0.33.5:
    sharp: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /signal-exit/4.1.0:
    signal-exit: private
  /simple-swizzle/0.2.2:
    simple-swizzle: private
  /slash/3.0.0:
    slash: private
  /smart-buffer/4.2.0:
    smart-buffer: private
  /snake-case/2.1.0:
    snake-case: private
  /socks-proxy-agent/8.0.5:
    socks-proxy-agent: private
  /socks/2.8.4:
    socks: private
  /sonner/1.7.4(react-dom@19.1.0)(react@19.1.0):
    sonner: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map/0.6.1:
    source-map: private
  /spawndamnit/3.0.1:
    spawndamnit: private
  /sprintf-js/1.0.3:
    sprintf-js: private
  /streamsearch/1.1.0:
    streamsearch: private
  /string-width/4.2.3:
    string-width: private
    string-width-cjs: private
  /string_decoder/1.3.0:
    string_decoder: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom/3.0.0:
    strip-bom: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /styled-jsx/5.1.6(react@19.1.0):
    styled-jsx: private
  /sucrase/3.35.0:
    sucrase: private
  /supports-color/5.5.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /swap-case/1.1.2:
    swap-case: private
  /tailwind-merge/2.6.0:
    tailwind-merge: private
  /tailwindcss-animate/1.0.7(tailwindcss@3.4.17):
    tailwindcss-animate: private
  /tailwindcss-rtl/0.9.0:
    tailwindcss-rtl: private
  /tailwindcss/3.4.17:
    tailwindcss: private
  /term-size/2.2.1:
    term-size: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /through/2.3.8:
    through: private
  /tiny-invariant/1.3.3:
    tiny-invariant: private
  /tinycolor2/1.6.0:
    tinycolor2: private
  /tinygradient/1.1.5:
    tinygradient: private
  /title-case/2.1.1:
    title-case: private
  /tmp/0.0.33:
    tmp: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /ts-node/10.9.2(@types/node@22.15.29)(typescript@5.8.3):
    ts-node: private
  /tslib/2.8.1:
    tslib: private
  /turbo-darwin-64/1.13.4:
    turbo-darwin-64: private
  /turbo-darwin-arm64/1.13.4:
    turbo-darwin-arm64: private
  /turbo-linux-64/1.13.4:
    turbo-linux-64: private
  /turbo-linux-arm64/1.13.4:
    turbo-linux-arm64: private
  /turbo-windows-64/1.13.4:
    turbo-windows-64: private
  /turbo-windows-arm64/1.13.4:
    turbo-windows-arm64: private
  /type-check/0.4.0:
    type-check: private
  /type-fest/0.20.2:
    type-fest: private
  /uglify-js/3.19.3:
    uglify-js: private
  /undici-types/6.21.0:
    undici-types: private
  /universalify/2.0.1:
    universalify: private
  /update-browserslist-db/1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  /update-check/1.5.4:
    update-check: private
  /upper-case-first/1.1.2:
    upper-case-first: private
  /upper-case/1.1.3:
    upper-case: private
  /uri-js/4.4.1:
    uri-js: private
  /use-callback-ref/1.3.3(@types/react@19.1.6)(react@19.1.0):
    use-callback-ref: private
  /use-intl/3.26.5(react@19.1.0):
    use-intl: private
  /use-sidecar/1.1.3(@types/react@19.1.6)(react@19.1.0):
    use-sidecar: private
  /use-sync-external-store/1.5.0(react@19.1.0):
    use-sync-external-store: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /v8-compile-cache-lib/3.0.1:
    v8-compile-cache-lib: private
  /validate-npm-package-name/5.0.1:
    validate-npm-package-name: private
  /vaul/0.9.9(@types/react-dom@19.1.5)(@types/react@19.1.6)(react-dom@19.1.0)(react@19.1.0):
    vaul: private
  /victory-vendor/36.9.2:
    victory-vendor: private
  /wcwidth/1.0.1:
    wcwidth: private
  /which/2.0.2:
    which: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wordwrap/1.0.0:
    wordwrap: private
  /wrap-ansi/6.2.0:
    wrap-ansi: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrappy/1.0.2:
    wrappy: private
  /yaml/2.8.0:
    yaml: private
  /yn/3.1.1:
    yn: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  /zod/3.25.46:
    zod: private
  /zustand/4.5.7(@types/react@19.1.6)(react@19.1.0):
    zustand: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.6
pendingBuilds: []
prunedAt: Sun, 01 Jun 2025 22:53:49 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@emnapi/runtime/1.4.3
  - /@esbuild/aix-ppc64/0.25.5
  - /@esbuild/android-arm/0.25.5
  - /@esbuild/android-arm64/0.25.5
  - /@esbuild/android-x64/0.25.5
  - /@esbuild/darwin-x64/0.25.5
  - /@esbuild/freebsd-arm64/0.25.5
  - /@esbuild/freebsd-x64/0.25.5
  - /@esbuild/linux-arm/0.25.5
  - /@esbuild/linux-arm64/0.25.5
  - /@esbuild/linux-ia32/0.25.5
  - /@esbuild/linux-loong64/0.25.5
  - /@esbuild/linux-mips64el/0.25.5
  - /@esbuild/linux-ppc64/0.25.5
  - /@esbuild/linux-riscv64/0.25.5
  - /@esbuild/linux-s390x/0.25.5
  - /@esbuild/linux-x64/0.25.5
  - /@esbuild/netbsd-arm64/0.25.5
  - /@esbuild/netbsd-x64/0.25.5
  - /@esbuild/openbsd-arm64/0.25.5
  - /@esbuild/openbsd-x64/0.25.5
  - /@esbuild/sunos-x64/0.25.5
  - /@esbuild/win32-arm64/0.25.5
  - /@esbuild/win32-ia32/0.25.5
  - /@esbuild/win32-x64/0.25.5
  - /@img/sharp-darwin-x64/0.33.5
  - /@img/sharp-libvips-darwin-x64/1.0.4
  - /@img/sharp-libvips-linux-arm/1.0.5
  - /@img/sharp-libvips-linux-arm64/1.0.4
  - /@img/sharp-libvips-linux-s390x/1.0.4
  - /@img/sharp-libvips-linux-x64/1.0.4
  - /@img/sharp-libvips-linuxmusl-arm64/1.0.4
  - /@img/sharp-libvips-linuxmusl-x64/1.0.4
  - /@img/sharp-linux-arm/0.33.5
  - /@img/sharp-linux-arm64/0.33.5
  - /@img/sharp-linux-s390x/0.33.5
  - /@img/sharp-linux-x64/0.33.5
  - /@img/sharp-linuxmusl-arm64/0.33.5
  - /@img/sharp-linuxmusl-x64/0.33.5
  - /@img/sharp-wasm32/0.33.5
  - /@img/sharp-win32-ia32/0.33.5
  - /@img/sharp-win32-x64/0.33.5
  - /@next/swc-darwin-x64/15.2.4
  - /@next/swc-linux-arm64-gnu/15.2.4
  - /@next/swc-linux-arm64-musl/15.2.4
  - /@next/swc-linux-x64-gnu/15.2.4
  - /@next/swc-linux-x64-musl/15.2.4
  - /@next/swc-win32-arm64-msvc/15.2.4
  - /@next/swc-win32-x64-msvc/15.2.4
  - /@rollup/rollup-android-arm-eabi/4.41.1
  - /@rollup/rollup-android-arm64/4.41.1
  - /@rollup/rollup-darwin-x64/4.41.1
  - /@rollup/rollup-freebsd-arm64/4.41.1
  - /@rollup/rollup-freebsd-x64/4.41.1
  - /@rollup/rollup-linux-arm-gnueabihf/4.41.1
  - /@rollup/rollup-linux-arm-musleabihf/4.41.1
  - /@rollup/rollup-linux-arm64-gnu/4.41.1
  - /@rollup/rollup-linux-arm64-musl/4.41.1
  - /@rollup/rollup-linux-loongarch64-gnu/4.41.1
  - /@rollup/rollup-linux-powerpc64le-gnu/4.41.1
  - /@rollup/rollup-linux-riscv64-gnu/4.41.1
  - /@rollup/rollup-linux-riscv64-musl/4.41.1
  - /@rollup/rollup-linux-s390x-gnu/4.41.1
  - /@rollup/rollup-linux-x64-gnu/4.41.1
  - /@rollup/rollup-linux-x64-musl/4.41.1
  - /@rollup/rollup-win32-arm64-msvc/4.41.1
  - /@rollup/rollup-win32-ia32-msvc/4.41.1
  - /@rollup/rollup-win32-x64-msvc/4.41.1
  - /turbo-darwin-64/1.13.4
  - /turbo-linux-64/1.13.4
  - /turbo-linux-arm64/1.13.4
  - /turbo-windows-64/1.13.4
  - /turbo-windows-arm64/1.13.4
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
