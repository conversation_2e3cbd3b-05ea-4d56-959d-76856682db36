{"version": 3, "sources": ["../src/index.ts", "../src/mappings.ts", "../src/path.ts", "../src/debug.ts"], "sourcesContent": ["import _debug from 'debug'\nimport * as fs from 'fs'\nimport globRex from 'globrex'\nimport { resolve } from 'path'\nimport type {\n  TSConfckParseNativeResult,\n  TSConfckParseOptions,\n  TSConfckParseResult,\n} from 'tsconfck'\nimport type { CompilerOptions } from 'typescript'\nimport { inspect } from 'util'\nimport { normalizePath, Plugin, searchForWorkspaceRoot } from 'vite'\nimport { resolvePathMappings } from './mappings'\nimport { basename, dirname, isAbsolute, join, relative } from './path'\nimport { PluginOptions } from './types'\nimport { debug, debugResolve } from './debug'\n\nconst notApplicable = [undefined, false] as const\nconst notFound = [undefined, true] as const\n\ntype ViteResolve = (id: string, importer: string) => Promise<string | undefined>\n\ntype Resolver = (\n  viteResolve: ViteResolve,\n  id: string,\n  importer: string\n) => Promise<readonly [resolved: string | undefined, matched: boolean]>\n\nexport type { PluginOptions }\n\nexport default (opts: PluginOptions = {}): Plugin => {\n  let resolversByDir: Record<string, Resolver[]>\n\n  return {\n    name: 'vite-tsconfig-paths',\n    enforce: 'pre',\n    async configResolved(config) {\n      let projectRoot = config.root\n      let workspaceRoot!: string\n\n      let { root } = opts\n      if (root) {\n        root = resolve(projectRoot, root)\n      } else {\n        workspaceRoot = searchForWorkspaceRoot(projectRoot)\n      }\n\n      debug('options.root   ==', root)\n      debug('project root   ==', projectRoot)\n      debug('workspace root ==', workspaceRoot)\n\n      // The \"root\" option overrides both of these.\n      if (root) {\n        projectRoot = root\n        workspaceRoot = root\n      }\n\n      const tsconfck = await import('tsconfck')\n\n      const projects = opts.projects\n        ? opts.projects.map((file) => {\n            if (!file.endsWith('.json')) {\n              file = join(file, 'tsconfig.json')\n            }\n            return resolve(projectRoot, file)\n          })\n        : await tsconfck.findAll(workspaceRoot, {\n            configNames: opts.configNames || ['tsconfig.json', 'jsconfig.json'],\n            skip(dir) {\n              if (dir === '.git' || dir === 'node_modules') {\n                return true\n              }\n              if (typeof opts.skip === 'function') {\n                return opts.skip(dir)\n              }\n              return false\n            },\n          })\n\n      debug('projects:', projects)\n\n      let hasTypeScriptDep = false\n      if (opts.parseNative) {\n        try {\n          const pkgJson = fs.readFileSync(\n            join(workspaceRoot, 'package.json'),\n            'utf8'\n          )\n          const pkg = JSON.parse(pkgJson)\n          const deps = { ...pkg.dependencies, ...pkg.devDependencies }\n          hasTypeScriptDep = 'typescript' in deps\n        } catch (e: any) {\n          if (e.code != 'ENOENT') {\n            throw e\n          }\n        }\n      }\n\n      let firstError: any\n\n      const parseOptions: TSConfckParseOptions = {\n        cache: new tsconfck.TSConfckCache(),\n      }\n\n      const parsedProjects = new Set(\n        await Promise.all(\n          projects.map((tsconfigFile) => {\n            if (tsconfigFile === null) {\n              debug('tsconfig file not found:', tsconfigFile)\n              return null\n            }\n            return (\n              hasTypeScriptDep\n                ? tsconfck.parseNative(tsconfigFile, parseOptions)\n                : tsconfck.parse(tsconfigFile, parseOptions)\n            ).catch((error) => {\n              if (opts.ignoreConfigErrors) {\n                debug('tsconfig file caused a parsing error:', tsconfigFile)\n              } else {\n                config.logger.error(\n                  '[tsconfig-paths] An error occurred while parsing \"' +\n                    tsconfigFile +\n                    '\". See below for details.' +\n                    (firstError\n                      ? ''\n                      : ' To disable this message, set the `ignoreConfigErrors` option to true.'),\n                  { error }\n                )\n                if (config.logger.hasErrorLogged(error)) {\n                  console.error(error)\n                }\n                firstError = error\n              }\n              return null\n            })\n          })\n        )\n      )\n\n      resolversByDir = {}\n      parsedProjects.forEach((project) => {\n        if (!project) {\n          return\n        }\n        // Don't create a resolver for projects with a references array.\n        // Instead, create a resolver for each project in that array.\n        if (project.referenced) {\n          project.referenced.forEach((projectRef) => {\n            parsedProjects.add(projectRef)\n          })\n          // Reinsert the parent project so it's tried last. This is\n          // important because project references can be used to\n          // override the parent project.\n          parsedProjects.delete(project)\n          parsedProjects.add(project)\n          project.referenced = undefined\n        } else {\n          const resolver = createResolver(project)\n          if (resolver) {\n            const projectDir = normalizePath(dirname(project.tsconfigFile))\n            const resolvers = (resolversByDir[projectDir] ||= [])\n            resolvers.push(resolver)\n          }\n        }\n      })\n    },\n    async resolveId(id, importer, options) {\n      if (debugResolve.enabled) {\n        debugResolve('resolving:', { id, importer })\n      }\n\n      if (!importer) {\n        debugResolve('importer is empty or undefined. skipping...')\n        return\n      }\n      if (relativeImportRE.test(id)) {\n        debugResolve('id is a relative import. skipping...')\n        return\n      }\n      if (isAbsolute(id)) {\n        debugResolve('id is an absolute path. skipping...')\n        return\n      }\n      if (id.includes('\\0')) {\n        debugResolve('id is a virtual module. skipping...')\n        return\n      }\n\n      // For Vite 4 and under, skipSelf needs to be set.\n      const resolveOptions = { ...options, skipSelf: true }\n      const viteResolve: ViteResolve = async (id, importer) =>\n        (await this.resolve(id, importer, resolveOptions))?.id\n\n      let prevProjectDir: string | undefined\n      let projectDir = normalizePath(dirname(importer))\n\n      // Find the nearest directory with a matching tsconfig file.\n      loop: while (projectDir && projectDir != prevProjectDir) {\n        const resolvers = resolversByDir[projectDir]\n        if (resolvers) {\n          for (const resolve of resolvers) {\n            const [resolved, matched] = await resolve(viteResolve, id, importer)\n            if (resolved) {\n              return resolved\n            }\n            if (matched) {\n              // Once a matching resolver is found, stop looking.\n              break loop\n            }\n          }\n        }\n        prevProjectDir = projectDir\n        projectDir = dirname(prevProjectDir)\n      }\n    },\n  }\n\n  type TsConfig = {\n    files?: string[]\n    include?: string[]\n    exclude?: string[]\n    compilerOptions?: CompilerOptions\n  }\n\n  function resolvePathsRootDir(\n    project: TSConfckParseResult | TSConfckParseNativeResult\n  ): string {\n    if ('result' in project) {\n      return (\n        project.result.options?.pathsBasePath ?? dirname(project.tsconfigFile)\n      )\n    }\n    const baseUrl = (project.tsconfig as TsConfig).compilerOptions?.baseUrl\n    if (baseUrl) {\n      return baseUrl\n    }\n    const projectWithPaths = project.extended?.find(\n      (p) => (p.tsconfig as TsConfig).compilerOptions?.paths\n    )\n    return dirname((projectWithPaths ?? project).tsconfigFile)\n  }\n\n  function createResolver(\n    project: TSConfckParseResult | TSConfckParseNativeResult\n  ): Resolver | null {\n    const configPath = normalizePath(project.tsconfigFile)\n    const config = project.tsconfig as TsConfig\n\n    debug('config loaded:', inspect({ configPath, config }, false, 10, true))\n\n    // Sometimes a tsconfig is not meant to be used for path resolution,\n    // but rather for pointing to other tsconfig files and possibly\n    // being extended by them. This is represented by an explicitly\n    // empty \"files\" array and a missing/empty \"include\" array.\n    if (config.files?.length == 0 && !config.include?.length) {\n      debug(\n        `[!] skipping \"${configPath}\" as no files can be matched since \"files\" is empty and \"include\" is missing or empty`\n      )\n      return null\n    }\n\n    const options = config.compilerOptions || {}\n    const { baseUrl, paths } = options\n    if (!baseUrl && !paths) {\n      debug(`[!] missing baseUrl and paths: \"${configPath}\"`)\n      return null\n    }\n\n    type InternalResolver = (\n      viteResolve: ViteResolve,\n      id: string,\n      importer: string\n    ) => Promise<string | undefined>\n\n    const resolveWithBaseUrl: InternalResolver | undefined = baseUrl\n      ? (viteResolve, id, importer) => {\n          const absoluteId = join(baseUrl, id)\n          debugResolve('trying with baseUrl:', absoluteId)\n          return viteResolve(absoluteId, importer)\n        }\n      : undefined\n\n    let resolveId: InternalResolver\n    if (paths) {\n      const pathsRootDir = resolvePathsRootDir(project)\n      const pathMappings = resolvePathMappings(paths, pathsRootDir)\n\n      const resolveWithPaths: InternalResolver = async (\n        viteResolve,\n        id,\n        importer\n      ) => {\n        for (const mapping of pathMappings) {\n          const match = id.match(mapping.pattern)\n          if (!match) {\n            continue\n          }\n          for (let pathTemplate of mapping.paths) {\n            let starCount = 0\n            const mappedId = pathTemplate.replace(/\\*/g, () => {\n              // There may exist more globs in the path template than in\n              // the match pattern. In that case, we reuse the final\n              // glob match.\n              const matchIndex = Math.min(++starCount, match.length - 1)\n              return match[matchIndex]\n            })\n            debugResolve('found match, trying to resolve:', mappedId)\n            const resolved = await viteResolve(mappedId, importer)\n            if (resolved) {\n              return resolved\n            }\n          }\n        }\n      }\n\n      if (resolveWithBaseUrl) {\n        resolveId = (viteResolve, id, importer) =>\n          resolveWithPaths(viteResolve, id, importer).then((resolved) => {\n            return resolved ?? resolveWithBaseUrl(viteResolve, id, importer)\n          })\n      } else {\n        resolveId = resolveWithPaths\n      }\n    } else {\n      resolveId = resolveWithBaseUrl!\n    }\n\n    const configDir = dirname(configPath)\n\n    // When `tsconfck.parseNative` is used, the outDir is absolute,\n    // which is not what `getIncluder` expects.\n    let { outDir } = options\n    if (outDir && isAbsolute(outDir)) {\n      outDir = relative(configDir, outDir)\n    }\n\n    const isIncludedRelative = getIncluder(\n      config.include?.map((p) => ensureRelative(configDir, p)),\n      config.exclude?.map((p) => ensureRelative(configDir, p)),\n      outDir\n    )\n\n    const importerExtRE = opts.loose\n      ? /./\n      : options.allowJs || basename(configPath).startsWith('jsconfig.')\n      ? jsLikeRE\n      : /\\.[mc]?tsx?$/\n\n    const resolutionCache = new Map<string, string>()\n\n    return async (viteResolve, id, importer) => {\n      // Ideally, Vite would normalize the importer path for us.\n      importer = normalizePath(importer)\n\n      // Remove query and hash parameters from the importer path.\n      const importerFile = importer.replace(/[#?].+$/, '')\n\n      // Ignore importers with unsupported extensions.\n      if (!importerExtRE.test(importerFile)) {\n        debugResolve('importer has unsupported extension. skipping...')\n        return notApplicable\n      }\n\n      // Respect the include/exclude properties.\n      const relativeImporterFile = relative(configDir, importerFile)\n      if (!isIncludedRelative(relativeImporterFile)) {\n        debugResolve('importer is not included. skipping...')\n        return notApplicable\n      }\n\n      // Find and remove Vite's suffix (e.g. \"?url\") if present.\n      // If the path is resolved, the suffix will be added back.\n      const suffix = /\\?.+$/.exec(id)?.[0]\n      if (suffix) {\n        id = id.slice(0, -suffix.length)\n      }\n\n      let resolvedId = resolutionCache.get(id)\n      if (!resolvedId) {\n        resolvedId = await resolveId(viteResolve, id, importer)\n        if (!resolvedId) {\n          return notFound\n        }\n        resolutionCache.set(id, resolvedId)\n        if (debugResolve.enabled) {\n          debugResolve('resolved without error:', {\n            id,\n            importer,\n            resolvedId,\n            configPath,\n          })\n        }\n      }\n\n      // Restore the suffix if one was removed earlier.\n      if (suffix) {\n        resolvedId += suffix\n      }\n\n      return [resolvedId, true]\n    }\n  }\n}\n\nconst jsLikeRE = /\\.(vue|svelte|mdx|[mc]?[jt]sx?)$/\nconst relativeImportRE = /^\\.\\.?(\\/|$)/\nconst defaultInclude = ['**/*']\nconst defaultExclude = [\n  '**/node_modules',\n  '**/bower_components',\n  '**/jspm_packages',\n]\n\n/**\n * The returned function does not support absolute paths.\n * Be sure to call `path.relative` on your path first.\n */\nfunction getIncluder(\n  includePaths = defaultInclude,\n  excludePaths = defaultExclude,\n  outDir?: string\n) {\n  if (outDir) {\n    excludePaths = excludePaths.concat(outDir)\n  }\n  if (includePaths.length || excludePaths.length) {\n    const includers: RegExp[] = []\n    const excluders: RegExp[] = []\n\n    includePaths.forEach(addCompiledGlob, includers)\n    excludePaths.forEach(addCompiledGlob, excluders)\n    debug(`compiled globs:`, { includers, excluders })\n\n    return (path: string) => {\n      path = path.replace(/\\?.+$/, '')\n      if (!relativeImportRE.test(path)) {\n        path = './' + path\n      }\n      const test = (glob: RegExp) => glob.test(path)\n      return includers.some(test) && !excluders.some(test)\n    }\n  }\n  return () => true\n}\n\nfunction addCompiledGlob(this: RegExp[], glob: string) {\n  const endsWithGlob = glob.split('/').pop()!.includes('*')\n  const relativeGlob = relativeImportRE.test(glob) ? glob : './' + glob\n  if (endsWithGlob) {\n    this.push(compileGlob(relativeGlob))\n  } else {\n    // Append a globstar to possible directories.\n    this.push(compileGlob(relativeGlob + '/**'))\n    // Try to match specific files (must have file extension).\n    if (/\\.\\w+$/.test(glob)) {\n      this.push(compileGlob(relativeGlob))\n    }\n  }\n}\n\nfunction compileGlob(glob: string) {\n  return globRex(glob, {\n    extended: true,\n    globstar: true,\n  }).regex\n}\n\nfunction ensureRelative(dir: string, path: string) {\n  return isAbsolute(path) ? relative(dir, path) : path\n}\n", "import { resolve } from 'path'\n\nexport type PathMapping = {\n  pattern: RegExp\n  paths: string[]\n}\n\nexport function resolvePathMappings(\n  paths: Record<string, string[]>,\n  base: string\n) {\n  // If a module name can be matched with multiple patterns then pattern\n  // with the longest prefix will be picked.\n  const sortedPatterns = Object.keys(paths).sort(\n    (a: string, b: string) => getPrefixLength(b) - getPrefixLength(a)\n  )\n  const resolved: PathMapping[] = []\n  for (let pattern of sortedPatterns) {\n    const relativePaths = paths[pattern]\n    pattern = escapeStringRegexp(pattern).replace(/\\*/g, '(.+)')\n    resolved.push({\n      pattern: new RegExp('^' + pattern + '$'),\n      paths: relativePaths.map((relativePath) => resolve(base, relativePath)),\n    })\n  }\n  return resolved\n}\n\nfunction getPrefixLength(pattern: string): number {\n  const prefixLength = pattern.indexOf('*')\n  return pattern.substr(0, prefixLength).length\n}\n\n// Adapted from:\n// https://github.com/sindresorhus/escape-string-regexp/blob/ba9a4473850cb367936417e97f1f2191b7cc67dd/index.js\n//\n// MIT License\n//\n// Copyright (c) Sindre Sorhus <<EMAIL>> (https://\n// sindresorhus.com)\n//\nfunction escapeStringRegexp(string: string) {\n  // Escape characters with special meaning either inside or outside\n  // character sets. Use a simple backslash escape when it’s always\n  // valid, and a `\\xnn` escape when the simpler form would be\n  // disallowed by Unicode patterns’ stricter grammar.\n  return string.replace(/[|\\\\{}()[\\]^$+?.]/g, '\\\\$&').replace(/-/g, '\\\\x2d')\n}\n", "import * as os from 'os'\nimport * as path from 'path'\nimport { normalizePath } from 'vite'\n\nconst isWindows = os.platform() == 'win32'\n\nexport const resolve = isWindows\n  ? (...paths: string[]) => normalizePath(path.win32.resolve(...paths))\n  : path.posix.resolve\n\nexport const isAbsolute = isWindows\n  ? path.win32.isAbsolute\n  : path.posix.isAbsolute\n\n/** Only call this on normalized paths */\nexport const join = path.posix.join\n\n/** Only call this on normalized paths */\nexport const relative = path.posix.relative\n\n/** Only call this on normalized paths */\nexport const basename = path.posix.basename\n\nexport { dirname } from 'path'\n", "import createDebug from 'debug'\n\nexport const debug = createDebug('vite-tsconfig-paths')\nexport const debugResolve = createDebug('vite-tsconfig-paths:resolve')\n\nif (process.env.TEST) {\n  createDebug.log = console.log.bind(console)\n}\n"], "mappings": ";AACA,YAAY,QAAQ;AACpB,OAAO,aAAa;AACpB,SAAS,WAAAA,gBAAe;AAOxB,SAAS,eAAe;AACxB,SAAS,iBAAAC,gBAAuB,8BAA8B;;;ACX9D,SAAS,eAAe;AAOjB,SAAS,oBACd,OACA,MACA;AAGA,QAAM,iBAAiB,OAAO,KAAK,KAAK,EAAE;AAAA,IACxC,CAAC,GAAW,MAAc,gBAAgB,CAAC,IAAI,gBAAgB,CAAC;AAAA,EAClE;AACA,QAAM,WAA0B,CAAC;AACjC,WAAS,WAAW,gBAAgB;AAClC,UAAM,gBAAgB,MAAM,OAAO;AACnC,cAAU,mBAAmB,OAAO,EAAE,QAAQ,OAAO,MAAM;AAC3D,aAAS,KAAK;AAAA,MACZ,SAAS,IAAI,OAAO,MAAM,UAAU,GAAG;AAAA,MACvC,OAAO,cAAc,IAAI,CAAC,iBAAiB,QAAQ,MAAM,YAAY,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,SAAyB;AAChD,QAAM,eAAe,QAAQ,QAAQ,GAAG;AACxC,SAAO,QAAQ,OAAO,GAAG,YAAY,EAAE;AACzC;AAUA,SAAS,mBAAmB,QAAgB;AAK1C,SAAO,OAAO,QAAQ,sBAAsB,MAAM,EAAE,QAAQ,MAAM,OAAO;AAC3E;;;AC/CA,YAAY,QAAQ;AACpB,YAAY,UAAU;AACtB,SAAS,qBAAqB;AAqB9B,SAAS,eAAe;AAnBxB,IAAM,YAAe,YAAS,KAAK;AAE5B,IAAMC,WAAU,YACnB,IAAI,UAAoB,cAAmB,WAAM,QAAQ,GAAG,KAAK,CAAC,IAC7D,WAAM;AAER,IAAM,aAAa,YACjB,WAAM,aACN,WAAM;AAGR,IAAM,OAAY,WAAM;AAGxB,IAAM,WAAgB,WAAM;AAG5B,IAAM,WAAgB,WAAM;;;ACrBnC,OAAO,iBAAiB;AAEjB,IAAM,QAAQ,YAAY,qBAAqB;AAC/C,IAAM,eAAe,YAAY,6BAA6B;AAErE,IAAI,QAAQ,IAAI,MAAM;AACpB,cAAY,MAAM,QAAQ,IAAI,KAAK,OAAO;AAC5C;;;AHUA,IAAM,gBAAgB,CAAC,QAAW,KAAK;AACvC,IAAM,WAAW,CAAC,QAAW,IAAI;AAYjC,IAAO,cAAQ,CAAC,OAAsB,CAAC,MAAc;AACnD,MAAI;AAEJ,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM,eAAe,QAAQ;AAC3B,UAAI,cAAc,OAAO;AACzB,UAAI;AAEJ,UAAI,EAAE,KAAK,IAAI;AACf,UAAI,MAAM;AACR,eAAOC,SAAQ,aAAa,IAAI;AAAA,MAClC,OAAO;AACL,wBAAgB,uBAAuB,WAAW;AAAA,MACpD;AAEA,YAAM,qBAAqB,IAAI;AAC/B,YAAM,qBAAqB,WAAW;AACtC,YAAM,qBAAqB,aAAa;AAGxC,UAAI,MAAM;AACR,sBAAc;AACd,wBAAgB;AAAA,MAClB;AAEA,YAAM,WAAW,MAAM,OAAO,UAAU;AAExC,YAAM,WAAW,KAAK,WAClB,KAAK,SAAS,IAAI,CAAC,SAAS;AAC1B,YAAI,CAAC,KAAK,SAAS,OAAO,GAAG;AAC3B,iBAAO,KAAK,MAAM,eAAe;AAAA,QACnC;AACA,eAAOA,SAAQ,aAAa,IAAI;AAAA,MAClC,CAAC,IACD,MAAM,SAAS,QAAQ,eAAe;AAAA,QACpC,aAAa,KAAK,eAAe,CAAC,iBAAiB,eAAe;AAAA,QAClE,KAAK,KAAK;AACR,cAAI,QAAQ,UAAU,QAAQ,gBAAgB;AAC5C,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,KAAK,SAAS,YAAY;AACnC,mBAAO,KAAK,KAAK,GAAG;AAAA,UACtB;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAEL,YAAM,aAAa,QAAQ;AAE3B,UAAI,mBAAmB;AACvB,UAAI,KAAK,aAAa;AACpB,YAAI;AACF,gBAAM,UAAa;AAAA,YACjB,KAAK,eAAe,cAAc;AAAA,YAClC;AAAA,UACF;AACA,gBAAM,MAAM,KAAK,MAAM,OAAO;AAC9B,gBAAM,OAAO,EAAE,GAAG,IAAI,cAAc,GAAG,IAAI,gBAAgB;AAC3D,6BAAmB,gBAAgB;AAAA,QACrC,SAAS,GAAP;AACA,cAAI,EAAE,QAAQ,UAAU;AACtB,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UAAI;AAEJ,YAAM,eAAqC;AAAA,QACzC,OAAO,IAAI,SAAS,cAAc;AAAA,MACpC;AAEA,YAAM,iBAAiB,IAAI;AAAA,QACzB,MAAM,QAAQ;AAAA,UACZ,SAAS,IAAI,CAAC,iBAAiB;AAC7B,gBAAI,iBAAiB,MAAM;AACzB,oBAAM,4BAA4B,YAAY;AAC9C,qBAAO;AAAA,YACT;AACA,oBACE,mBACI,SAAS,YAAY,cAAc,YAAY,IAC/C,SAAS,MAAM,cAAc,YAAY,GAC7C,MAAM,CAAC,UAAU;AACjB,kBAAI,KAAK,oBAAoB;AAC3B,sBAAM,yCAAyC,YAAY;AAAA,cAC7D,OAAO;AACL,uBAAO,OAAO;AAAA,kBACZ,uDACE,eACA,+BACC,aACG,KACA;AAAA,kBACN,EAAE,MAAM;AAAA,gBACV;AACA,oBAAI,OAAO,OAAO,eAAe,KAAK,GAAG;AACvC,0BAAQ,MAAM,KAAK;AAAA,gBACrB;AACA,6BAAa;AAAA,cACf;AACA,qBAAO;AAAA,YACT,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF;AAEA,uBAAiB,CAAC;AAClB,qBAAe,QAAQ,CAAC,YAAY;AAClC,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAGA,YAAI,QAAQ,YAAY;AACtB,kBAAQ,WAAW,QAAQ,CAAC,eAAe;AACzC,2BAAe,IAAI,UAAU;AAAA,UAC/B,CAAC;AAID,yBAAe,OAAO,OAAO;AAC7B,yBAAe,IAAI,OAAO;AAC1B,kBAAQ,aAAa;AAAA,QACvB,OAAO;AACL,gBAAM,WAAW,eAAe,OAAO;AACvC,cAAI,UAAU;AACZ,kBAAM,aAAaC,eAAc,QAAQ,QAAQ,YAAY,CAAC;AAC9D,kBAAM,YAAa,4DAA+B,CAAC;AACnD,sBAAU,KAAK,QAAQ;AAAA,UACzB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,MAAM,UAAU,IAAI,UAAU,SAAS;AACrC,UAAI,aAAa,SAAS;AACxB,qBAAa,cAAc,EAAE,IAAI,SAAS,CAAC;AAAA,MAC7C;AAEA,UAAI,CAAC,UAAU;AACb,qBAAa,6CAA6C;AAC1D;AAAA,MACF;AACA,UAAI,iBAAiB,KAAK,EAAE,GAAG;AAC7B,qBAAa,sCAAsC;AACnD;AAAA,MACF;AACA,UAAI,WAAW,EAAE,GAAG;AAClB,qBAAa,qCAAqC;AAClD;AAAA,MACF;AACA,UAAI,GAAG,SAAS,IAAI,GAAG;AACrB,qBAAa,qCAAqC;AAClD;AAAA,MACF;AAGA,YAAM,iBAAiB,EAAE,GAAG,SAAS,UAAU,KAAK;AACpD,YAAM,cAA2B,OAAOC,KAAIC,cAAU;AA9L5D;AA+LS,2BAAM,KAAK,QAAQD,KAAIC,WAAU,cAAc,MAA/C,mBAAmD;AAAA;AAEtD,UAAI;AACJ,UAAI,aAAaF,eAAc,QAAQ,QAAQ,CAAC;AAGhD;AAAM,eAAO,cAAc,cAAc,gBAAgB;AACvD,gBAAM,YAAY,eAAe,UAAU;AAC3C,cAAI,WAAW;AACb,uBAAWD,YAAW,WAAW;AAC/B,oBAAM,CAAC,UAAU,OAAO,IAAI,MAAMA,SAAQ,aAAa,IAAI,QAAQ;AACnE,kBAAI,UAAU;AACZ,uBAAO;AAAA,cACT;AACA,kBAAI,SAAS;AAEX,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AACA,2BAAiB;AACjB,uBAAa,QAAQ,cAAc;AAAA,QACrC;AAAA,IACF;AAAA,EACF;AASA,WAAS,oBACP,SACQ;AAlOZ;AAmOI,QAAI,YAAY,SAAS;AACvB,cACE,mBAAQ,OAAO,YAAf,mBAAwB,kBAAxB,YAAyC,QAAQ,QAAQ,YAAY;AAAA,IAEzE;AACA,UAAM,WAAW,aAAQ,SAAsB,oBAA9B,mBAA+C;AAChE,QAAI,SAAS;AACX,aAAO;AAAA,IACT;AACA,UAAM,oBAAmB,aAAQ,aAAR,mBAAkB;AAAA,MACzC,CAAC,MAAG;AA7OV,YAAAI;AA6Oc,gBAAAA,MAAA,EAAE,SAAsB,oBAAxB,gBAAAA,IAAyC;AAAA;AAAA;AAEnD,WAAO,SAAS,8CAAoB,SAAS,YAAY;AAAA,EAC3D;AAEA,WAAS,eACP,SACiB;AApPrB;AAqPI,UAAM,aAAaH,eAAc,QAAQ,YAAY;AACrD,UAAM,SAAS,QAAQ;AAEvB,UAAM,kBAAkB,QAAQ,EAAE,YAAY,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC;AAMxE,UAAI,YAAO,UAAP,mBAAc,WAAU,KAAK,GAAC,YAAO,YAAP,mBAAgB,SAAQ;AACxD;AAAA,QACE,iBAAiB;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,OAAO,mBAAmB,CAAC;AAC3C,UAAM,EAAE,SAAS,MAAM,IAAI;AAC3B,QAAI,CAAC,WAAW,CAAC,OAAO;AACtB,YAAM,mCAAmC,aAAa;AACtD,aAAO;AAAA,IACT;AAQA,UAAM,qBAAmD,UACrD,CAAC,aAAa,IAAI,aAAa;AAC7B,YAAM,aAAa,KAAK,SAAS,EAAE;AACnC,mBAAa,wBAAwB,UAAU;AAC/C,aAAO,YAAY,YAAY,QAAQ;AAAA,IACzC,IACA;AAEJ,QAAI;AACJ,QAAI,OAAO;AACT,YAAM,eAAe,oBAAoB,OAAO;AAChD,YAAM,eAAe,oBAAoB,OAAO,YAAY;AAE5D,YAAM,mBAAqC,OACzC,aACA,IACA,aACG;AACH,mBAAW,WAAW,cAAc;AAClC,gBAAM,QAAQ,GAAG,MAAM,QAAQ,OAAO;AACtC,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,mBAAS,gBAAgB,QAAQ,OAAO;AACtC,gBAAI,YAAY;AAChB,kBAAM,WAAW,aAAa,QAAQ,OAAO,MAAM;AAIjD,oBAAM,aAAa,KAAK,IAAI,EAAE,WAAW,MAAM,SAAS,CAAC;AACzD,qBAAO,MAAM,UAAU;AAAA,YACzB,CAAC;AACD,yBAAa,mCAAmC,QAAQ;AACxD,kBAAM,WAAW,MAAM,YAAY,UAAU,QAAQ;AACrD,gBAAI,UAAU;AACZ,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,oBAAoB;AACtB,oBAAY,CAAC,aAAa,IAAI,aAC5B,iBAAiB,aAAa,IAAI,QAAQ,EAAE,KAAK,CAAC,aAAa;AAC7D,iBAAO,8BAAY,mBAAmB,aAAa,IAAI,QAAQ;AAAA,QACjE,CAAC;AAAA,MACL,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF,OAAO;AACL,kBAAY;AAAA,IACd;AAEA,UAAM,YAAY,QAAQ,UAAU;AAIpC,QAAI,EAAE,OAAO,IAAI;AACjB,QAAI,UAAU,WAAW,MAAM,GAAG;AAChC,eAAS,SAAS,WAAW,MAAM;AAAA,IACrC;AAEA,UAAM,qBAAqB;AAAA,OACzB,YAAO,YAAP,mBAAgB,IAAI,CAAC,MAAM,eAAe,WAAW,CAAC;AAAA,OACtD,YAAO,YAAP,mBAAgB,IAAI,CAAC,MAAM,eAAe,WAAW,CAAC;AAAA,MACtD;AAAA,IACF;AAEA,UAAM,gBAAgB,KAAK,QACvB,MACA,QAAQ,WAAW,SAAS,UAAU,EAAE,WAAW,WAAW,IAC9D,WACA;AAEJ,UAAM,kBAAkB,oBAAI,IAAoB;AAEhD,WAAO,OAAO,aAAa,IAAI,aAAa;AA9VhD,UAAAG;AAgWM,iBAAWH,eAAc,QAAQ;AAGjC,YAAM,eAAe,SAAS,QAAQ,WAAW,EAAE;AAGnD,UAAI,CAAC,cAAc,KAAK,YAAY,GAAG;AACrC,qBAAa,iDAAiD;AAC9D,eAAO;AAAA,MACT;AAGA,YAAM,uBAAuB,SAAS,WAAW,YAAY;AAC7D,UAAI,CAAC,mBAAmB,oBAAoB,GAAG;AAC7C,qBAAa,uCAAuC;AACpD,eAAO;AAAA,MACT;AAIA,YAAM,UAASG,MAAA,QAAQ,KAAK,EAAE,MAAf,gBAAAA,IAAmB;AAClC,UAAI,QAAQ;AACV,aAAK,GAAG,MAAM,GAAG,CAAC,OAAO,MAAM;AAAA,MACjC;AAEA,UAAI,aAAa,gBAAgB,IAAI,EAAE;AACvC,UAAI,CAAC,YAAY;AACf,qBAAa,MAAM,UAAU,aAAa,IAAI,QAAQ;AACtD,YAAI,CAAC,YAAY;AACf,iBAAO;AAAA,QACT;AACA,wBAAgB,IAAI,IAAI,UAAU;AAClC,YAAI,aAAa,SAAS;AACxB,uBAAa,2BAA2B;AAAA,YACtC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAGA,UAAI,QAAQ;AACV,sBAAc;AAAA,MAChB;AAEA,aAAO,CAAC,YAAY,IAAI;AAAA,IAC1B;AAAA,EACF;AACF;AAEA,IAAM,WAAW;AACjB,IAAM,mBAAmB;AACzB,IAAM,iBAAiB,CAAC,MAAM;AAC9B,IAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF;AAMA,SAAS,YACP,eAAe,gBACf,eAAe,gBACf,QACA;AACA,MAAI,QAAQ;AACV,mBAAe,aAAa,OAAO,MAAM;AAAA,EAC3C;AACA,MAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,UAAM,YAAsB,CAAC;AAC7B,UAAM,YAAsB,CAAC;AAE7B,iBAAa,QAAQ,iBAAiB,SAAS;AAC/C,iBAAa,QAAQ,iBAAiB,SAAS;AAC/C,UAAM,mBAAmB,EAAE,WAAW,UAAU,CAAC;AAEjD,WAAO,CAACC,UAAiB;AACvB,MAAAA,QAAOA,MAAK,QAAQ,SAAS,EAAE;AAC/B,UAAI,CAAC,iBAAiB,KAAKA,KAAI,GAAG;AAChC,QAAAA,QAAO,OAAOA;AAAA,MAChB;AACA,YAAM,OAAO,CAAC,SAAiB,KAAK,KAAKA,KAAI;AAC7C,aAAO,UAAU,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,IAAI;AAAA,IACrD;AAAA,EACF;AACA,SAAO,MAAM;AACf;AAEA,SAAS,gBAAgC,MAAc;AACrD,QAAM,eAAe,KAAK,MAAM,GAAG,EAAE,IAAI,EAAG,SAAS,GAAG;AACxD,QAAM,eAAe,iBAAiB,KAAK,IAAI,IAAI,OAAO,OAAO;AACjE,MAAI,cAAc;AAChB,SAAK,KAAK,YAAY,YAAY,CAAC;AAAA,EACrC,OAAO;AAEL,SAAK,KAAK,YAAY,eAAe,KAAK,CAAC;AAE3C,QAAI,SAAS,KAAK,IAAI,GAAG;AACvB,WAAK,KAAK,YAAY,YAAY,CAAC;AAAA,IACrC;AAAA,EACF;AACF;AAEA,SAAS,YAAY,MAAc;AACjC,SAAO,QAAQ,MAAM;AAAA,IACnB,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC,EAAE;AACL;AAEA,SAAS,eAAe,KAAaA,OAAc;AACjD,SAAO,WAAWA,KAAI,IAAI,SAAS,KAAKA,KAAI,IAAIA;AAClD;", "names": ["resolve", "normalizePath", "resolve", "resolve", "normalizePath", "id", "importer", "_a", "path"]}