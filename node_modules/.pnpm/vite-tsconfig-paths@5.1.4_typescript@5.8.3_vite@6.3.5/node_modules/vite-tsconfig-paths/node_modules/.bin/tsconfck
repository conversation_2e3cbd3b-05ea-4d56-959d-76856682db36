#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/tsconfck@3.1.6_typescript@5.8.3/node_modules/tsconfck/bin/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/tsconfck@3.1.6_typescript@5.8.3/node_modules/tsconfck/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/tsconfck@3.1.6_typescript@5.8.3/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/tsconfck@3.1.6_typescript@5.8.3/node_modules/tsconfck/bin/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/tsconfck@3.1.6_typescript@5.8.3/node_modules/tsconfck/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/tsconfck@3.1.6_typescript@5.8.3/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../tsconfck@3.1.6_typescript@5.8.3/node_modules/tsconfck/bin/tsconfck.js" "$@"
else
  exec node  "$basedir/../../../../../tsconfck@3.1.6_typescript@5.8.3/node_modules/tsconfck/bin/tsconfck.js" "$@"
fi
