#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/next@15.2.4_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/bin/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/next@15.2.4_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/next@15.2.4_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0/node_modules/next/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/next@15.2.4_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/next@15.2.4_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/bin/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/next@15.2.4_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/next@15.2.4_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0/node_modules/next/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/next@15.2.4_@babel+core@7.27.4_react-dom@19.1.0_react@19.1.0/node_modules:/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/bin/next" "$@"
else
  exec node  "$basedir/../../dist/bin/next" "$@"
fi
