
> @mtbrmg/frontend@1.0.0 dev /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend
> next dev --port 3001

   ▲ Next.js 15.2.4
   - Local:        http://localhost:3001
   - Network:      http://***********:3001
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 1835ms
 ○ Compiling / ...
 ✓ Compiled / in 3.5s (997 modules)
 GET / 200 in 3896ms
 ✓ Compiled in 430ms (439 modules)
 ○ Compiling /founder-dashboard ...
 ✓ Compiled /founder-dashboard in 1492ms (1371 modules)
 GET /founder-dashboard 200 in 1574ms
 GET / 200 in 153ms
 GET /founder-dashboard 200 in 13ms
 ○ Compiling /founder-dashboard/clients ...
 ✓ Compiled /founder-dashboard/clients in 718ms (1440 modules)
 GET /founder-dashboard/clients 200 in 804ms
 ✓ Compiled /founder-dashboard/projects in 354ms (1473 modules)
 GET /founder-dashboard/projects 200 in 393ms
 ○ Compiling /founder-dashboard/tasks ...
 ✓ Compiled /founder-dashboard/tasks in 508ms (1514 modules)
 GET /founder-dashboard/tasks 200 in 553ms
 ○ Compiling /founder-dashboard/finance/reports ...
 ✓ Compiled /founder-dashboard/finance/reports in 1033ms (1493 modules)
 GET /founder-dashboard/finance/reports 200 in 1215ms
 ✓ Compiled /_not-found in 494ms (1497 modules)
 GET /founder-dashboard/finance/reports/generate?type=profit_loss 404 in 531ms
 GET /founder-dashboard/finance/reports/generate?type=profit_loss 404 in 90ms
 GET /founder-dashboard/finance/reports 200 in 122ms
 ○ Compiling /founder-dashboard/analytics ...
 ✓ Compiled /founder-dashboard/analytics in 518ms (1508 modules)
 GET /founder-dashboard/analytics 200 in 550ms
 GET /founder-dashboard/finance/reports/generate?type=profit_loss 404 in 20ms
 GET /founder-dashboard/finance/reports/generate?type=profit_loss 404 in 73ms
 GET /founder-dashboard/finance/reports 200 in 66ms
 ✓ Compiled in 2.2s (689 modules)
 GET /founder-dashboard/finance/reports 200 in 84ms
 ⨯ ./lib/api/commissions.ts:1:1
Module not found: Can't resolve './client'
> 1 | import { apiClient } from './client';
    | ^
  2 |
  3 | export interface Commission {
  4 |   id: string;

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./lib/api.ts
./app/founder-dashboard/clients/page.tsx
 ○ Compiling /_error ...
 ⨯ ./lib/api/commissions.ts:1:1
Module not found: Can't resolve './client'
> 1 | import { apiClient } from './client';
    | ^
  2 |
  3 | export interface Commission {
  4 |   id: string;

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./lib/api.ts
./app/founder-dashboard/clients/page.tsx
 GET /founder-dashboard/finance/reports 500 in 6298ms
 GET /founder-dashboard/finance/reports 500 in 13ms
 GET /founder-dashboard/finance/reports 500 in 7ms
 GET /founder-dashboard/finance/reports 500 in 6ms
 GET /founder-dashboard/finance/reports 500 in 8ms
 ⨯ ./lib/api/commissions.ts:1:1
Module not found: Can't resolve './client'
> 1 | import { apiClient } from './client';
    | ^
  2 |
  3 | export interface Commission {
  4 |   id: string;

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./lib/api.ts
./app/founder-dashboard/clients/page.tsx
 ⨯ ./lib/api/commissions.ts:1:1
Module not found: Can't resolve './client'
> 1 | import { apiClient } from './client';
    | ^
  2 |
  3 | export interface Commission {
  4 |   id: string;

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./lib/api.ts
./app/founder-dashboard/clients/page.tsx
 GET /founder-dashboard/finance/reports 500 in 77ms
 GET /founder-dashboard 500 in 32ms
 ✓ Compiled /_not-found in 3.4s (1832 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET /_next/static/webpack/2dfd677a4fbf8ee8.webpack.hot-update.json 404 in 1973ms
 GET /founder-dashboard 200 in 107ms
 GET /founder-dashboard 200 in 13ms
 ✓ Compiled in 2.7s (1832 modules)
