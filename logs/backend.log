/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

System check identified some issues:

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 7 issues (0 silenced).
June 04, 2025 - 20:30:21
Django version 4.2.9, using settings 'mtbrmg_erp.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/projects/ HTTP/1.1" 200 1479
"OPTIONS /api/reports/?period=current_month HTTP/1.1" 200 0
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"OPTIONS /api/reports/summary/ HTTP/1.1" 200 0
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Exception in thread django-main-thread:
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/management/commands/runserver.py", line 125, in inner_run
    autoreload.raise_last_exception()
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/autoreload.py", line 87, in raise_last_exception
    raise _exception[1]
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/management/__init__.py", line 394, in execute
    autoreload.check_errors(django.setup)()
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/apps/registry.py", line 91, in populate
    app_config = AppConfig.create(entry)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/apps/config.py", line 193, in create
    import_module(entry)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'commissions'
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

System check identified some issues:

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 7 issues (0 silenced).
June 04, 2025 - 20:46:39
Django version 4.2.9, using settings 'mtbrmg_erp.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

System check identified some issues:

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 7 issues (0 silenced).
June 04, 2025 - 20:47:19
Django version 4.2.9, using settings 'mtbrmg_erp.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

System check identified some issues:

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 7 issues (0 silenced).
June 04, 2025 - 20:47:40
Django version 4.2.9, using settings 'mtbrmg_erp.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/urls.py changed, reloading.
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

Exception in thread django-main-thread:
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/management/commands/runserver.py", line 133, in inner_run
    self.check(display_num_errors=True)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/management/base.py", line 485, in check
    all_issues = checks.run_checks(
                 ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/checks/registry.py", line 88, in run_checks
    new_errors = check(app_configs=app_configs, databases=databases)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/checks/urls.py", line 14, in check_url_config
    return check_resolver(resolver)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/checks/urls.py", line 24, in check_resolver
    return check_method()
           ^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/urls/resolvers.py", line 494, in check
    for pattern in self.url_patterns:
                   ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/urls/resolvers.py", line 715, in url_patterns
    patterns = getattr(self.urlconf_module, "urlpatterns", self.urlconf_module)
                       ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/urls/resolvers.py", line 708, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/urls.py", line 42, in <module>
    path("api/v1/", include('commissions.urls')),
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/urls/conf.py", line 38, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/commissions/urls.py", line 3, in <module>
    from .views import CommissionViewSet, CommissionRuleViewSet, CommissionPaymentViewSet
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/commissions/views.py", line 9, in <module>
    from .models import Commission, CommissionRule, CommissionPayment
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/commissions/models.py", line 8, in <module>
    class CommissionRule(models.Model):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/db/models/base.py", line 134, in __new__
    raise RuntimeError(
RuntimeError: Model class commissions.models.CommissionRule doesn't declare an explicit app_label and isn't in an application in INSTALLED_APPS.
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

Exception in thread django-main-thread:
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/management/commands/runserver.py", line 133, in inner_run
    self.check(display_num_errors=True)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/management/base.py", line 556, in check
    raise SystemCheckError(msg)
django.core.management.base.SystemCheckError: SystemCheckError: System check identified some issues:

ERRORS:
commissions.Commission: (models.E034) The index name 'commission_sales_rep_status_idx' cannot be longer than 30 characters.
commissions.CommissionPayment: (models.E034) The index name 'commission_payment_commission_idx' cannot be longer than 30 characters.
commissions.CommissionPayment: (models.E034) The index name 'commission_payment_status_date_idx' cannot be longer than 30 characters.

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 10 issues (0 silenced).
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/commissions/models.py changed, reloading.
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

Exception in thread django-main-thread:
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/management/commands/runserver.py", line 133, in inner_run
    self.check(display_num_errors=True)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/management/base.py", line 556, in check
    raise SystemCheckError(msg)
django.core.management.base.SystemCheckError: SystemCheckError: System check identified some issues:

ERRORS:
commissions.CommissionPayment: (models.E034) The index name 'commission_payment_commission_idx' cannot be longer than 30 characters.
commissions.CommissionPayment: (models.E034) The index name 'commission_payment_status_date_idx' cannot be longer than 30 characters.

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 9 issues (0 silenced).
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/commissions/models.py changed, reloading.
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

System check identified some issues:

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 7 issues (0 silenced).
June 04, 2025 - 20:56:54
Django version 4.2.9, using settings 'mtbrmg_erp.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
