
> @mtbrmg/frontend@1.0.0 dev /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend
> vite --port 3001

5:41:55 PM [vite] (client) Re-optimizing dependencies because lockfile has changed

  VITE v6.3.5  ready in 643 ms

  ➜  Local:   http://localhost:3001/
  ➜  Network: http://***********:3001/
5:42:33 PM [vite] Pre-transform error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
5:45:03 PM [vite] Pre-transform error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
5:45:03 PM [vite] Internal server error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
      at __node_internal_ (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:225:9)
      at new NodeError (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:195:5)
      at packageResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:899:9)
      at moduleResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:939:18)
      at defaultResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1017:15)
      at resolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1030:12)
      at tryImportMetaResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:149:45)
      at resolveStandardizedNameForImport (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:174:19)
      at resolveStandardizedName (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:186:22)
      at loadPlugin (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:56:7)
      at loadPlugin.next (<anonymous>)
      at createDescriptor (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:140:16)
      at createDescriptor.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:261:32)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:44:11
      at Array.forEach (<anonymous>)
      at Function.async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:43:15)
      at Function.all (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:216:13)
      at Generator.next (<anonymous>)
      at createDescriptors (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:102:41)
      at createDescriptors.next (<anonymous>)
      at createPluginDescriptors (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:99:17)
      at createPluginDescriptors.next (<anonymous>)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:65:32
      at Generator.next (<anonymous>)
      at Function.<anonymous> (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/gensync-utils/async.js:21:3)
      at Generator.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:269:25)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at Function.errback (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:113:7)
      at errback (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/gensync-utils/async.js:65:18)
      at async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:188:17)
      at onFirstPause (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:216:13)
      at Generator.next (<anonymous>)
      at cachedFunction (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/caching.js:52:46)
      at cachedFunction.next (<anonymous>)
      at mergeChainOpts (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:349:34)
      at mergeChainOpts.next (<anonymous>)
      at chainWalker (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:316:14)
      at chainWalker.next (<anonymous>)
      at buildRootChain (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:56:36)
      at buildRootChain.next (<anonymous>)
      at loadPrivatePartialConfig (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/partial.js:72:62)
      at loadPrivatePartialConfig.next (<anonymous>)
      at loadFullConfig (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/full.js:36:46)
      at loadFullConfig.next (<anonymous>)
      at transform (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/transform.js:20:44)
      at transform.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:269:25)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:93:9
      at new Promise (<anonymous>)
      at async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:92:14)
      at stopHiding - secret - don't use this - v1 (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js:47:12)
      at Module.transformAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/transform.js:45:77)
      at TransformPluginContext.handler (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@vitejs+plugin-react@4.5.1_vite@6.3.5/node_modules/@vitejs/plugin-react/dist/index.cjs:256:37)
      at async EnvironmentPluginContainer.transform (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42294:18)
      at async loadAndTransform (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35735:27)
      at async viteTransformMiddleware (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:37250:24)
5:45:08 PM [vite] Pre-transform error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
5:45:08 PM [vite] Internal server error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
      at __node_internal_ (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:225:9)
      at new NodeError (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:195:5)
      at packageResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:899:9)
      at moduleResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:939:18)
      at defaultResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1017:15)
      at resolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1030:12)
      at tryImportMetaResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:149:45)
      at resolveStandardizedNameForImport (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:174:19)
      at resolveStandardizedName (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:186:22)
      at loadPlugin (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:56:7)
      at loadPlugin.next (<anonymous>)
      at createDescriptor (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:140:16)
      at createDescriptor.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:261:32)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:44:11
      at Array.forEach (<anonymous>)
      at Function.async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:43:15)
      at Function.all (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:216:13)
      at Generator.next (<anonymous>)
      at createDescriptors (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:102:41)
      at createDescriptors.next (<anonymous>)
      at createPluginDescriptors (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:99:17)
      at createPluginDescriptors.next (<anonymous>)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:65:32
      at Generator.next (<anonymous>)
      at Function.<anonymous> (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/gensync-utils/async.js:21:3)
      at Generator.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:269:25)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at Function.errback (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:113:7)
      at errback (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/gensync-utils/async.js:65:18)
      at async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:188:17)
      at onFirstPause (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:216:13)
      at Generator.next (<anonymous>)
      at cachedFunction (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/caching.js:52:46)
      at cachedFunction.next (<anonymous>)
      at mergeChainOpts (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:349:34)
      at mergeChainOpts.next (<anonymous>)
      at chainWalker (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:316:14)
      at chainWalker.next (<anonymous>)
      at buildRootChain (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:56:36)
      at buildRootChain.next (<anonymous>)
      at loadPrivatePartialConfig (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/partial.js:72:62)
      at loadPrivatePartialConfig.next (<anonymous>)
      at loadFullConfig (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/full.js:36:46)
      at loadFullConfig.next (<anonymous>)
      at transform (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/transform.js:20:44)
      at transform.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:269:25)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:93:9
      at new Promise (<anonymous>)
      at async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:92:14)
      at stopHiding - secret - don't use this - v1 (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js:47:12)
      at Module.transformAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/transform.js:45:77)
      at TransformPluginContext.handler (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@vitejs+plugin-react@4.5.1_vite@6.3.5/node_modules/@vitejs/plugin-react/dist/index.cjs:256:37)
      at async EnvironmentPluginContainer.transform (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42294:18)
      at async loadAndTransform (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35735:27)
      at async viteTransformMiddleware (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:37250:24)
5:45:28 PM [vite] Pre-transform error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
5:45:28 PM [vite] Internal server error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
      at __node_internal_ (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:225:9)
      at new NodeError (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:195:5)
      at packageResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:899:9)
      at moduleResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:939:18)
      at defaultResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1017:15)
      at resolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1030:12)
      at tryImportMetaResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:149:45)
      at resolveStandardizedNameForImport (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:174:19)
      at resolveStandardizedName (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:186:22)
      at loadPlugin (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:56:7)
      at loadPlugin.next (<anonymous>)
      at createDescriptor (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:140:16)
      at createDescriptor.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:261:32)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:44:11
      at Array.forEach (<anonymous>)
      at Function.async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:43:15)
      at Function.all (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:216:13)
      at Generator.next (<anonymous>)
      at createDescriptors (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:102:41)
      at createDescriptors.next (<anonymous>)
      at createPluginDescriptors (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:99:17)
      at createPluginDescriptors.next (<anonymous>)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:65:32
      at Generator.next (<anonymous>)
      at Function.<anonymous> (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/gensync-utils/async.js:21:3)
      at Generator.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:269:25)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at Function.errback (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:113:7)
      at errback (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/gensync-utils/async.js:65:18)
      at async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:188:17)
      at onFirstPause (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:216:13)
      at Generator.next (<anonymous>)
      at cachedFunction (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/caching.js:52:46)
      at cachedFunction.next (<anonymous>)
      at mergeChainOpts (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:349:34)
      at mergeChainOpts.next (<anonymous>)
      at chainWalker (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:316:14)
      at chainWalker.next (<anonymous>)
      at buildRootChain (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:56:36)
      at buildRootChain.next (<anonymous>)
      at loadPrivatePartialConfig (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/partial.js:72:62)
      at loadPrivatePartialConfig.next (<anonymous>)
      at loadFullConfig (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/full.js:36:46)
      at loadFullConfig.next (<anonymous>)
      at transform (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/transform.js:20:44)
      at transform.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:269:25)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:93:9
      at new Promise (<anonymous>)
      at async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:92:14)
      at stopHiding - secret - don't use this - v1 (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js:47:12)
      at Module.transformAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/transform.js:45:77)
      at TransformPluginContext.handler (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@vitejs+plugin-react@4.5.1_vite@6.3.5/node_modules/@vitejs/plugin-react/dist/index.cjs:256:37)
      at async EnvironmentPluginContainer.transform (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42294:18)
      at async loadAndTransform (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35735:27)
      at async viteTransformMiddleware (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:37250:24)
5:45:30 PM [vite] Pre-transform error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
5:45:30 PM [vite] Internal server error: Cannot find package '@babel/plugin-transform-react-jsx' imported from /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/babel-virtual-resolve-base.js
  Plugin: vite:react-babel
  File: /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/src/main.tsx
      at __node_internal_ (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:225:9)
      at new NodeError (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:195:5)
      at packageResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:899:9)
      at moduleResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:939:18)
      at defaultResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1017:15)
      at resolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1030:12)
      at tryImportMetaResolve (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:149:45)
      at resolveStandardizedNameForImport (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:174:19)
      at resolveStandardizedName (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:186:22)
      at loadPlugin (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/files/plugins.js:56:7)
      at loadPlugin.next (<anonymous>)
      at createDescriptor (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:140:16)
      at createDescriptor.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:261:32)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:44:11
      at Array.forEach (<anonymous>)
      at Function.async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:43:15)
      at Function.all (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:216:13)
      at Generator.next (<anonymous>)
      at createDescriptors (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:102:41)
      at createDescriptors.next (<anonymous>)
      at createPluginDescriptors (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:99:17)
      at createPluginDescriptors.next (<anonymous>)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-descriptors.js:65:32
      at Generator.next (<anonymous>)
      at Function.<anonymous> (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/gensync-utils/async.js:21:3)
      at Generator.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:269:25)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at Function.errback (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:113:7)
      at errback (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/gensync-utils/async.js:65:18)
      at async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:188:17)
      at onFirstPause (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:216:13)
      at Generator.next (<anonymous>)
      at cachedFunction (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/caching.js:52:46)
      at cachedFunction.next (<anonymous>)
      at mergeChainOpts (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:349:34)
      at mergeChainOpts.next (<anonymous>)
      at chainWalker (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:316:14)
      at chainWalker.next (<anonymous>)
      at buildRootChain (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/config-chain.js:56:36)
      at buildRootChain.next (<anonymous>)
      at loadPrivatePartialConfig (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/partial.js:72:62)
      at loadPrivatePartialConfig.next (<anonymous>)
      at loadFullConfig (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/config/full.js:36:46)
      at loadFullConfig.next (<anonymous>)
      at transform (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/transform.js:20:44)
      at transform.next (<anonymous>)
      at step (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:269:25)
      at evaluateAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:291:5)
      at /Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:93:9
      at new Promise (<anonymous>)
      at async (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js:92:14)
      at stopHiding - secret - don't use this - v1 (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js:47:12)
      at Module.transformAsync (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@babel+core@7.27.4/node_modules/@babel/core/lib/transform.js:45:77)
      at TransformPluginContext.handler (/Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/@vitejs+plugin-react@4.5.1_vite@6.3.5/node_modules/@vitejs/plugin-react/dist/index.cjs:256:37)
      at async EnvironmentPluginContainer.transform (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42294:18)
      at async loadAndTransform (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35735:27)
      at async viteTransformMiddleware (file:///Users/<USER>/Sites/mtbrmg-erp-system/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:37250:24)
