# MTBRMG ERP - Development Guide

## 🚀 Quick Start (Stable Solution)

The fastest and most stable way to run the development environment on macOS:

```bash
# Start development servers
npm run dev
```

This will:
- ✅ Clean up any existing processes
- ✅ Start PostgreSQL and Redis
- ✅ Start Django backend on http://localhost:8000
- ✅ Start Next.js frontend on http://localhost:3001
- ✅ Show process IDs for easy management

## 🌐 Access Points

- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:8000/api/
- **Admin Panel**: http://localhost:8000/admin/
- **Login**: `founder` / `demo123`

## 🛠️ Available Scripts

### Primary Development Commands

```bash
# Start development (recommended)
npm run dev                 # Simple, stable development server

# Alternative development options
npm run dev:stable         # Advanced development server with more features
npm run dev:turbo          # Turbo-powered development (original)
```

### Individual Services

```bash
# Backend only
npm run dev:backend

# Frontend only  
npm run dev:frontend

# Database operations
npm run migrate
npm run create-founder
```

## 🔧 Development Scripts

### Simple Development (`npm run dev`)
- **File**: `scripts/dev-simple.sh`
- **Best for**: Daily development, stable performance
- **Features**: 
  - Automatic cleanup
  - Process management
  - Clear status reporting
  - Easy to stop with Ctrl+C

### Stable Development (`npm run dev:stable`)
- **File**: `scripts/dev-stable.sh`
- **Best for**: Advanced users, production-like setup
- **Features**:
  - Service management commands
  - Log file management
  - Status checking
  - Individual service control

## 🛑 Stopping Development Servers

### Method 1: Ctrl+C (Recommended)
Press `Ctrl+C` in the terminal where you ran `npm run dev`

### Method 2: Kill by Process ID
```bash
# Use the PIDs shown when starting
kill 86874 86883  # Replace with actual PIDs
```

### Method 3: Kill by Port
```bash
# Kill all processes on development ports
lsof -ti:8000,3001 | xargs kill
```

### Method 4: Using stable script
```bash
./scripts/dev-stable.sh stop
```

## 📊 Performance Optimizations

The development setup includes several Mac-specific optimizations:

- **Node.js**: `--max-old-space-size=4096` for better memory handling
- **Next.js**: Telemetry disabled for faster startup
- **Django**: `--noreload` flag for stable backend
- **Process Management**: Clean port cleanup to prevent conflicts

## 🐛 Troubleshooting

### Port Conflicts
```bash
# Check what's running on ports
lsof -i :8000
lsof -i :3001

# Kill specific port
lsof -ti:8000 | xargs kill -9
```

### Database Issues
```bash
# Reset database
npm run migrate

# Recreate founder user
npm run create-founder
```

### Clean Restart
```bash
# Stop everything
./scripts/dev-stable.sh stop

# Wait a moment
sleep 2

# Start fresh
npm run dev
```

## 📝 Logs

### View Real-time Logs
```bash
# Backend logs (if using stable script)
tail -f logs/backend.log

# Frontend logs
# Check the terminal output where you ran npm run dev
```

### Log Locations
- Backend: `logs/backend.log` (when using stable script)
- Frontend: Terminal output
- Django: Standard Django logging

## 🔄 Development Workflow

1. **Start Development**:
   ```bash
   npm run dev
   ```

2. **Access Application**:
   - Open http://localhost:3001
   - Login with `founder` / `demo123`

3. **Make Changes**:
   - Frontend changes auto-reload
   - Backend changes require restart

4. **Stop Development**:
   - Press `Ctrl+C`

## 💡 Tips

- **Faster Startup**: Keep PostgreSQL and Redis running in background
- **Memory Usage**: The setup is optimized for Mac performance
- **Process Management**: Each script shows clear process IDs
- **Clean Environment**: Scripts automatically clean up old processes

## 🚨 Known Issues

- Some Django warnings are normal (guardian, staticfiles, URL namespaces)
- First startup may take longer due to dependency installation
- If ports are busy, the script will clean them automatically

## 📞 Support

If you encounter issues:

1. Try a clean restart: `./scripts/dev-stable.sh stop && npm run dev`
2. Check for port conflicts: `lsof -i :8000,3001`
3. Verify services: `brew services list | grep -E "(postgresql|redis)"`
4. Check logs for specific error messages
